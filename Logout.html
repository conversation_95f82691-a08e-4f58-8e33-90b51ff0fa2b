





<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="pt" lang="pt">

<head>
<title>SIRE</title>

<!-- BEGIN INCLUDES-->

<link rel="SHORTCUT ICON" href="images/logos/unitel_img.png" />
<link rel="stylesheet" type="text/css" href="css/reset.css?21130" />
<link rel="stylesheet" type="text/css" href="css/custom.css?21130" />
<link rel="stylesheet" type="text/css" href="css/custom-theme/jquery-ui-1.10.1.custom.css?21130" />
<link rel="stylesheet" type="text/css" href="css/custom-theme/jquery.ui.selectmenu.css?21130" />
<link rel="stylesheet" href="css/custom-theme/jquery.fileupload.css?21130" />
<link rel="stylesheet" href="css/custom-theme/jquery.fileupload-ui.css?21130" />
<noscript><link rel="stylesheet" href="css/custom-theme/jquery.fileupload-noscript.css?21130" /></noscript>
<noscript><link rel="stylesheet" href="css/custom-theme/jquery.fileupload-ui-noscript.css?21130" /></noscript>

<link rel="stylesheet" type="text/css" href="css/jquery.clearable.css?21130" />
<link rel="stylesheet" type="text/css" href="css/jquery.ui.timepicker.css?21130" />
<link rel="stylesheet" type="text/css" href="css/datatable.css?21130" />
<link rel="stylesheet" type="text/css" href="css/header.css?21130" />
<link rel="stylesheet" type="text/css" href="css/search.css?21130" />
<link rel="stylesheet" type="text/css" href="css/details.css?21130" />
<link rel="stylesheet" type="text/css" href="css/maps.css?21130" />
<link rel="stylesheet" type="text/css" href="css/tec_comm_stats.css?21130" />
<link rel="stylesheet" type="text/css" href="css/jquery.treegrid.css?21130" />

	
<script type="text/javascript" src="js/accounting.js?21130"></script>
<script type="text/javascript" src="js/jquery-2.1.3.min.js?21130"></script>
<script type="text/javascript" src="js/jquery-ui-1.11.3.custom.min.js?21130"></script>
<script type="text/javascript" src="js/jquery.ui.datepicker-pt-PT.js?21130"></script>
<script type="text/javascript" src="js/jquery.ui.timepicker.js?21130"></script>
<script type="text/javascript" src="js/jquery.ui.timepicker-pt.js?21130"></script>
<script type="text/javascript" src="js/jquery.dataTables.js?21130"></script>
<script type="text/javascript" src="js/jquery.dataTables.extras.js?21130"></script>

<script type="text/javascript" src="js/jquery.ui.selectmenu.js?21130"></script>
<script type="text/javascript" src="js/jquery.clearable.js?21130"></script>

<script type="text/javascript" src="js/main.js?21130"></script>
<script type="text/javascript" src="js/transform.js?21130"></script>
<script type="text/javascript" src="js/maps_tec_geofencing.js?21130"></script>
<script type="text/javascript" src="js/tecs.js?21130"></script>

<script type="text/javascript" src="js/moment.min.js?21130"></script>

<script type="text/javascript" src="js/list.js?21130"></script>
<script type="text/javascript" src="js/search.js?21130"></script>
<script type="text/javascript" src="js/details.js?21130"></script>

<!-- END INCLUDES-->

</head>

<body>
	







	<div id="loadingScreen">
		<table>
			<tr><td><img src="images/icons/loading.gif"></img></td></tr>
		</table>
	</div>
	<div id="tableLoading">
		<table>
			<tr><td><img src="images/icons/loading_small.gif"></img></td></tr>
		</table>
	</div>
	<div id="operationsLoading">
		<table>
			<tr><td><img src="images/icons/loading_small.gif"></img></td></tr>
		</table>
	</div>
	<div id="warningFeedback" title="Erro" class="feedbackContainer">
		<table><tr>
			<td><img src="images/icons/warning.png"></img></td>
			<td id="warningText"></td>
		</tr></table>
	</div>
	<div id="successFeedback" title="Sucesso" class="feedbackContainer">
		<table><tr>
			<td><img src="images/icons/success.png"></img></td>
			<td id="successText"></td>
		</tr></table>
	</div>

	<div id="header">
		



<input type="hidden" id="language" value='pt' />
<script type="text/javascript" src="js/header.js?21130"></script>
<script type="text/javascript" src="js/load_externals.js?21130"></script>



<div class="userMenuContent">
	
</div>
<ul class="userMenu">
    
    <li class="menuItem"><a href="Logout">Terminar sessão</a></li>
</ul>


	<table class="globalNav">
		<tr>
		
		<td id="unitelLogo"></td>
			
				<td class="globalNavButton globalNavActive">
					Autenticação
				</td>
			
		
		</tr>
	</table>

<script type="text/javascript">
$(function() {
	header.buildMenus();
});

var campaignTypesDescriptionByNameI18N = {
		banner: 'Banner',
		merchandising: 'Merchandising',
		agent_of_the_month: 'Agente do mês',
		message_of_the_day: 'Mensagem do dia',
		news: 'Notícias',
		campaign: 'Campanha',
		circular_banner: 'Banner circular',
		support_message: 'Mensagem de apoio',
		stores: 'Lojas',
		delegates: 'Delegados'
	};


var productTypesDescriptionI18N = {
	s1: 'Voucher',
	s2: 'Carregamento directo',
	s3: 'Recargas Físicas'
};

var productTypesDescriptionByNameI18N = {
	voucher: 'Voucher',
	recharge: 'Carregamento directo',
	physicalrecharge: 'Recargas Físicas'
};

var transactionTotalTypesI18N = {
	voucher: 'Voucher',
	recharge: 'Carregamento directo',
	total: 'Total'
};

var transactionTypesI18N = {
	balance_credit: 'RSA',
	commission_payment: 'CA',
	sale: 'Venda',
	adjustment: 'Ajuste',
	refund: 'Estorno',
	balance_transfer: 'Transf. saldo',
};

var transactionStateI18N = {
		success: 'OK',
		no_msisdn: 'Erro',
		msisdn_not_provisioned: 'Erro',
		no_sapreference: 'Erro',
		no_sapreference_msisdn_association: 'Erro',
		unknown: 'Desconhecido',
	};

var mobileMoneyTransactionTypesI18N = {
		agent_credit: 'CA',
		agent_reinforcement: 'RA',
		beneficiary_transfer: 'TB',
	};
	
var tecAppTypesI18N = {
	sales_tec_app: 'TEC vendas',
	installer_tec_app: 'TEC instalador'
};

var mobileMoneyStatesI18N = {
	provisioned: 'Aprovisionado',
	not_provisioned: 'Não Aprovisionado',
	unknown: 'Desconhecido'
};

var entityTypesI18N = {
	agent: 'Agente',
	sub_agent: 'Sub-agente',
	store: 'Loja',
	assistant_seller: 'Assistente',
	supervisor_seller: 'Supervisor',
	tec: 'TEC',
	tec_installer_user: 'Utilizador de TEC instalador',
	sire_user: 'Utilizadores Sire',
	external_api_user: 'Utilizadores API Externa',
	unknown: 'Desconhecido',

	agents: 'Agentes',
	sub_agents: 'Sub-agentes',
	stores: 'Lojas',
	assistant_sellers: 'Assistentes',
	supervisor_sellers: 'Supervisores',
	tecs: 'TECs',
	tec_installer_users: 'Utilizadores de TEC instalador',
	sire_users: 'enum.entity.type.sire.users',
	external_api_users: 'enum.entity.type.external.api.users'
};

var tecTypesI18N = {
	t0: 'Vendas',
	t1: 'Instalador'
};

var tecTypesByNameI18N = {
	sales_tec: 'Vendas',
	installer_tec: 'Instalador'
};

var tecStatesI18N = {
	s0: 'Desconhecido',
	s1: 'Criado',
	s2: 'Confirmado',
	s3: 'Activo ',
	s4: 'Associado',
	s5: 'Inactivo ',
	s7: 'Removido ',
	s8: 'A Confirmar',
	s9: 'A Associar',
	s10: 'Stock agente',
	s11: 'Em associação Store'
};

var tecStatesByNameI18N = {
	unknown: 'Desconhecido',
	created: 'Criado',
	confirmed: 'Confirmado',
	active: 'Activo ',
	associated: 'Associado',
	inactive: 'Inactivo ',
	removed: 'Removido ',
	confirming: 'A Confirmar',
	associating: 'A Associar',
	agent_stock: 'Stock agente',
	associating_store: 'Em associação Store'
};

var simSetupStatesByNameI18N = {
	action_not_required: 'N/A',
	created: 'Criado',
	waiting_change_type: 'A alterar tipo ',
	change_type_ok: 'Alterar tipo - OK',
	change_type_fail: 'Alterar tipo - Falha',
	waiting_change_class: 'A alterar classe',
	change_class_ok: 'Alterar classe - OK',
	change_class_fail: 'Alterar classe - Falha',
	create_tec_fail: 'Falha',
	finished_ok: 'Sucesso',
	sms_notification_fail: 'Envio SMS - Falha'
};
	
var tecUpdateStatesByNameI18N = {
	initiated: 'Iniciado',
	finished: 'Terminado',
	aborted: 'Abortado',
	confirmed: 'Confirmado',
	damaged: 'Danificado',
};

var tecUpdateTypeByNameI18N = {
	ota: 'OTA',
	tec_installer_restore: 'Restauro',
	tec_installer_update: 'Actualização',
};

var alarmSeveritiesI18N = {
	critical: 'Crítica',
	major: 'Alta',
	minor: 'Baixa',
	warning: 'Aviso',
	notification: 'Notificação',
	clear: 'Clear',
	unknown: 'Indefinida'
};
var alarmTypesI18N = {
	access: 'Acesso',
	authentication: 'Autenticação',
	connection: 'Ligação',
	processing: 'Processamento',
	voucher: 'Voucher',
	undetermined: 'Indeterminado'
};

var eventTypesI18N = {
	creation: 'Registo no SIRE',
	state_change: 'Alteração de estado',
	blacklist: 'Lista negra',
	version_update: 'Instalação firmware'
};

var readWriteI18N = {
	read: 'Leitura',
	write: 'Escrita'
};

var channelsI18N = {
	unknown_channel: 'Desconhecido',
	bo: 'Backoffice',
	ia: 'Portal Agentes',
	tec_sms: 'TEC SMS',
	tec_gprs: 'TEC GPRS',
	system: 'Sistema',
	external_api: 'API externa',
	sap_sd: 'SAP',
	unitel: 'Unitel',
	atm: 'ATM',
	tec_installer: 'TEC Instalador',
	bo_v2: 'Backoffice (v2)',
	ia_v2: 'Portal Agentes (v2)',
	mobile_sms: 'Android SMS',
	mobile_gprs: 'Android GPRS',
	client_sms: 'Notificações do cliente',
	m_manager: 'mManager',
	mobile_gen: 'Mobile Generator',	
	eai_cr_mobile: 'Reg. Clientes (M)',
	eai_cr_sms: 'Reg. Clientes (SMS)',
	eai_cr_web: 'Reg. Clientes (Web)',
	eai: 'EAI Generico',
	all: 'Todos',
	p_api: 'API Parceiros',
	store_mobile_gprs: 'mSeller Store GPRS',
	store_mobile_sms: 'mSeller Store SMS',
	ussd: 'USSD',
	services_activation: 'Activação de Serviços',
	unitel_external_user: 'Utilizador externo Unitel',
	vpp: 'Vendas porta a porta',
	bulk: 'Carregamentos Bulk'
};

var deviceTypesI18N = {
	d0: 'TEC',
	d1: 'Android',
	d2: 'API Parceiros',
	d3: 'mSeller Store',
	d4: 'USSD',
	tec: 'TEC',
	android: 'Android',
	partnerapi: 'API Parceiros',
	storeandroid: 'mSeller Store',
	ussd: 'USSD'
};

var productConfigurationI18N = {
	general: 'Global',
	agent: 'Agente',
	store: 'Loja'
};

var profileTypesDescriptionI18N = {
	bo: 'Backoffice',
	ia: 'Portal Agentes',
	tec: 'Terminal'
};

var profileTypesI18N = {
	bo: 'BO',
	ia: 'IA',
	tec: 'TEC'
};
	
var provisionStatusCodesI18N = {
	ok: 'OK',
	msisdn_duplicate: 'MSISDN duplicado',
	serial_invalid: 'SERIAL inválido',
	msisdn_invalid: 'MSISDN inválido',
	device_type_unknown: 'Tipo de dispositivo desconhecido',
	imsi_invalid: 'IMSI inválido',
	iccid_invalid: 'ICCID inválido',
	imei_invalid: 'IMEI inválido',
	database_error: 'Erro de base de dados',
	parse_error: 'Erro de sintaxe'
};

var pinStatusCodesI18N = {
	ok: 'OK',
	unknown_sap_ref: 'Ref. SAP desconhecida',
	database_error: 'Erro de base de dados',
	parse_error: 'Erro de sintaxe',
	duplicated_sap: 'Ref. SAP duplicada',
	invalid_pin7_size: 'Tamanho pin inválido'
};

var auxTexts = {
	buttonOkText: 'OK',
	buttonConfirmText: 'Confirmar',
	buttonApplyText: 'Aplicar',
	buttonCancelText: 'Cancelar',
	buttonCleanText: 'Limpar',
	buttonRemoveText: 'Remover',
	buttonApplyGeofencingText: 'Definir limite',
	buttonRemoveGeofencingText: 'Remover limite',
	buttonPrevText: 'Anterior',
	buttonNextText: 'Próxima',
	errorListFieldsEmptyText: 'Deverá activar pelo menos um campo na listagem',
	errorValidateFiltersText: 'Deverá validar os filtros introduzidos',
	errorGeneric: 'Não foi possível aceder ao recurso pretendido',
	tecDeleted: 'Este TEC encontra-se apagado',
	tecBlacklisted: 'Este TEC encontra-se na lista negras',
	entityDeleted: 'Esta entidade encontra-se apagada',
	entityBlacklisted: 'Esta entidade encontra-se na lista negra',
	clickRowText: 'Clique na linha para aceder aos detalhes',
	unknown: 'Desconhecida',
	unknowninfo: 'Informação desconhecida',
	textNoChanges: 'Sem alterações',
	textWithChanges: 'Com alterações',
	textWithErrors: 'Com erros',
	errorValidatePropertyText: 'Os campos assinalados a vermelho deverão ser corrigidos',
	sessionExpired: 'Sessão expirada',
	confirmProcessOperationText: 'Irá [operation] o serviço [process] da máquina [machine].\nConfirma a operação?',
	opstart:'iniciar',
	oprestart:'reiniciar',
	opstop:'parar',
	daysText: 'dias',
	creditText: 'UTT',
	currencyText: 'AKZ',
	filterListByParam: 'Filtrar lista por este parâmetro',
	lastRefresh: 'Actualizado às ',
	active: 'Activo',
	inactive: 'Inactivo',
	filterTecAuditTrail: 'Consultar histórico de operações',
	seeTecDetails: 'Ver detalhes de TEC',
	seeSellerDetails: 'Ver detalhes de vendedor',
	mapViewInterval: 'Data visualização: ',
	mapViewIntervalCustom: 'de [0] a [1]',
	statsTecs: 'Dados estatísticos ',
	geoLimitTecs: 'Limite geográfico de terminais',
	autoRefresh: 'Activar/desactivar actualização automática ',
	backToChart: 'Voltar ao gráfico',
	selectToSeeMessagesList: 'Seleccione um ponto do gráfico para ver lista de mensagens',
	alarmClosed: 'Este alarme encontra-se fechado',
	alarmOpen:  'Este alarme encontra-se aberto',
	selectToSeeDetails: 'Seleccione um ponto do gráfico para ver mais detalhe',
	mandatoryUpdate: 'Actualização obrigatória',
	notInProduction: 'Esta versão encontra-se obsoleta',
	notAvailableForUpdate: 'Esta versão encontra-se indisponível para actualização',
	yes: 'Sim',
	no: 'Não',
	contextMenuTitle: 'Menu de contexto: [entity]',
	sendExportToMail: 'O ficheiro será enviado para: [email] ',
	exportToCSV: 'Exportar CSV',
	invalidMailAddress: 'E-mail inválido',
	invalidReason: 'Motivo inválido',
	confirmingTecPin: 'O PIN para confirmação do TEC é',
	confirmingTecTime: 'O modo de confirmação manter-se-á durante',
	confirmingTecExpired: 'O tempo para confirmação expirou',
	addNewVersion: 'Criar nova versão',
	generatedTecPin: 'O PIN para acesso ao TEC é',
	generatedIaPassword: 'A password para acesso ao Portal de Agentes é',
	generatedBOUserPassword: 'A password para acesso ao BO é',
	invalidName: 'Nome inválido',
	invalidMsisdn: 'MSISDN inválido',
	invalidAddress: 'Morada inválida',
	confirmAssociateTec: 'Irá associar o TEC [tecId] à loja [storeId].<br/>Confirma a operação?',
	associatingTecPin: 'O PIN para associação do TEC é',
	associatingTecTime: 'O modo de associação manter-se-á durante',
	associatingTecExpired: 'O tempo para associação expirou',
	confirmSellerTransfer: 'Irá transferir o vendedor [sellerId] para a loja [storeId].<br/>Confirma a operação?',
	createInstallerTecUser: 'Criar novo utilizador de TEC instalador',
	invalidValue: 'Valor inválido',
	invalidSapRef: 'Ref. SAP inválida',
	editAgentBalance: 'Irá fazer um [operation] de saldo no valor de [amount] ao agente [agentId].<br/>Confirma a operação?',
	reinforceBalance: 'reforço',
	adjustBalance: 'ajuste',
	viewTransactionsTotals: 'Ver totais',
	invalidSizeChangePassword: 'A nova palavra-passe deverá ter entre 6 e 8 caracteres',
	invalidConfirmChangePassword: 'Os campos Nova palavra-passe e Confirmar nova palavra-passe deverão são iguais',
	createSubAgent: 'Criar sub-agente',
	invalidNif: 'NIF inválido',
	invalidTargetEntityId: 'ID de destino inválido',
	confirmTransferBalance: 'Irá tansferir saldo no valor de [amount] da entidade [sourceEntityId] para a [targetEntityId].<br/>Confirma a operação?',
	confirmTransferTec: 'Irá tansferir o TEC [tecId] para a entidade [targetEntityId].<br/>Confirma a operação?',
	unlimited: 'Ilimitado',
	createProfile: 'Criar perfil',
	profileDetails: 'Detalhes de perfil',
	transactionRolledback: 'Esta transacção foi revertida',
	errorInvalidTreeSearchTerm: 'O termo de pesquisa deverá ter 3 ou mais caracteres',
	agentDetails: 'Detalhes de agente',
	subAgentDetails: 'Detalhes de sub-agente',
	storeDetails: 'Detalhes de loja',
	sellerDetails: 'Detalhes de vendedor',
	tecDetails: 'Detalhes de TEC',
	createdSellerId: 'O ID do novo vendedor é',
	createTec: 'Criar TEC',
	productInactive: 'Este produto encontra-se inactivo',
	productDetails: 'Detalhes de produto',
	createProduct: 'Criar produto',
	invalidSearchName: 'Nome de configuração pessoal inválido',
	loadPersonalSearch: 'Carregar "[0]"',
	confirmUpdateProductForEntity: 'Irá configurar o produto [productSubType] [productName] para a entidade [entityId].<br/>Confirma a operação?',
	confirmTecAppVersionForEntity: 'Irá configurar a versão [versionName] para a entidade [entityId].<br/>Confirma a operação?',
	hasPendingUpdates: 'Este TEC tem uma actualização pendente',
	retailerName: 'Retalhistas',
	operatorName: 'Unitel',
	createAgent: 'Criar agente',
	boUserDetails: 'Detalhes de utilizador BO',
	lastSale: 'Última venda',
	lastInstall: 'Última instalação',
	addBOUser: 'Criar utilizador BO',
	geofencingDisabled: 'Este terminal não tem limite geográfico definido',
	geofencing: 'Limite geográfico',
	warningChooseSites: 'Deverá definir a região pretendida',
	sitesLimitExceeded: 'Excedeu o numero máximo de sites seleccionados',
	addTerminalBatchProvision: 'Submeter lote de aprovisionamento',
	addRepositoryPinBatchProvision: 'Submeter lote de aprovisionamento',
	notAvailable: 'N/A',
	createAccessControlRule: 'Criar regra de acesso',
	errorNoDataToDisplay: 'Não existem dados para visualizar',
	labelTotal:'Total',
	transactionResolved: 'Esta transacção encontra-se resolvida',
	stockDeleted: 'Este item de stock encontra-se removido',
	ruleNotEditable: 'Esta regra não é editável',
	responseTimeMs:'Tempo resposta (ms)',
	dateHour:'Data/hora',
	statsAudit: 'Dados estatísticos ',
	invalidSubject: 'Não deve introduzir caracteres inválidos no campo assunto.',
	invalidMessage: 'Não deve introduzir caracteres inválidos no campo mensagem.',
	emptySubject: 'Campo assunto obrigatório.',
	emptyMessage: 'Campo mensagem obrigatório.',
	notificationDeleted: 'Esta notificação encontra-se apagada',
	notificationViewed: 'Esta notificação foi lida',
	subjectTooLong: 'Assunto ultrapassa o número máximo de caracteres.',
	messageTooLong: 'Mensagem ultrapassa o número máximo de caracteres.',
	selectAlLeastOneRecipient: 'Seleccione pelo menos um destinatário.',
	selectAtLeastOneHierarchy: 'Seleccione pelo menos uma hierarquia.',
	selectSellersForOperatorHierarchy: 'Seleccione pelo menos um tipo de vendedor para enviar mensagem para a hierarquia Unitel.',
	chooseAtLeastOneSeller: 'Seleccione pelo menos um tipo de vendedor.',
	openMManagerApp: 'Abrir',
	downloadMManagerApp: 'Instalar',
	errorOpeningManager: 'Deverá aceitar abrir a applicação mManager. Caso não esteja instalada terá de instalar.',
	ignoreMManagerApp: 'Ignorar',
	generatedMSellerPassword: 'A Password de acesso ao mSeller é',
	mSellerProvisioningSuccess: 'Terminal de mSeller criado com sucesso',
	mSellerProvisioningFail:'Erro ao criar terminal de mSeller',
	usernameMissing: 'Por favor insira o Utilizador',
	usernameAndPasswordMissing: 'Por favor insira o Utilizador e a Palavra-passe',
	usernameAndCodeMissing: 'Por favor insira o Utilizador e o Código',
	USSDProvisioningSuccess: 'Terminal USSD criado com sucesso',
	USSDProvisioningFail:'Erro a criar Terminal USSD',
	mMoneyProvisioningSuccess: 'Mobile Money criado com sucesso',
	mMoneyProvisioningFail:'Erro a associar Mobile Money',
	mMoneyDeprovisioningSuccess: 'Mobile Money desassociado com sucesso',
	mMoneyDeprovisioningFail:'Erro a desassociar Mobile Money',
	statsOperationHistory: 'Dados estatísticos ',
	createCampaign: 'Criar elemento',
	errorOnCreateCampaign: 'Ocorreu um erro a criar a campanha.',
	campaignDetails: 'Detalhes da campanha',
	campaignDelegatesDetails: 'Detalhes do delegado',
	campaignStoresDetails: 'Detalhes da loja',
	editError: 'Ocorreu um erro ao submeter as edições.',
	errorInvalidDate: 'Foi inserida uma data inválida.'
};

var formats = {
	chartDateOnly: 'dd-MM-yyyy',
	chartHourOnly: 'HH:mm',
	titleHourFormat: 'HH:mm',
	titleDayFormat: 'DD-MM-YYYY',
	
	dateSimple: 'DD-MM-YYYY HH:mm',
	dateComplete: 'DD-MM-YYYY HH:mm:ss',
	dateFull: 'DD-MM-YYYY HH:mm:ss.SSS',
	
	daySimple: 'DD-MM-YYYY',
	
	hourSimple: 'HH:mm',
	hourComplete: 'HH:mm:ss',
	hourFull: 'HH:mm:ss.SSS',
	
	amountPrecision: 2,
	amountThousand: 'space',
	amountDecimal: '.',
	amountFormat: '%v%s',
	amountMultiplier: 1000,
	
	currencyPrecision: 0,
	currencyThousand: 'space',
	currencyDecimal: '.',
	currencyFormat: '%v%s',
	currencyMultiplier: '1'
};

var entitiesLabels = {
	agent: 'Agente',
	subAgent: 'Sub-agente',
	installerTecUser: 'Utilizador TEC instalador',
	installerTec: 'TEC instalador',
	tec: 'TEC',
	seller: 'Vendedor',
	store: 'Loja',
	tecVersion: 'Versão de TEC',
	transaction: 'Transacção',
	product: 'Produto',
	accessControlRule: 'Regra de acesso'
};

var transactionsSummaryLabels = {
	operationType: 'Tipo operação',
	quantity: 'Quantidade',
	totalAmountUnits: 'Total UTT'
};

var regexs = {
	creditUnsigned: /^\d{1,13}\.{0,1}\d{0,2}$/,
	creditSigned: /^-?\d{1,13}\.{0,1}\d{0,2}$/,
	smallCreditUnsigned: /^\d{1,5}\.{0,1}\d{0,2}$/,
	currencyUnsigned: /^\d{1,14}$/,
	currencySigned: /^-?\d{1,14}$/,
	msisdnValidation: /^(244)(9)([2-4]|7){1}[0-9]{7}|^(9)([2-4]|7){1}[0-9]{7}$/
};

var geofencingProperties = {
	maxSitesSelection: 0
};

var servicesI18N = {
	none: 'Nenhum',
	security: 'Segurança',
	terminals: 'Terminais',
	entities: 'Entidades',
	transactions: 'Transacções',
	productcatalog: 'Catálogo de produtos',
	gis: 'GIS',
	systemmonitoringandcontrol: 'Monitorização e controlo',
	integration: 'Integração',
	usersettings: 'Configurações de utilizador',
	system: 'Sistema',
};

var apiI18N = {
	noapi: 'Sem API',
	rest: 'REST',
	webservice: 'Web service',
	sms: 'SMS'
};

var campaignI18n = {
		imageMaxSizeWarning:'Imagem demasiado grande. Adicione uma imagem mais pequena.',
		pdfMaxSizeWarning:'Pdf demasiado grande. Adicione um pdf mais pequeno.',
		excelMaxSizeWarning:'Ficheiro excel demasiado grande. Adicione um ficheiro mais pequeno.',
		errorOnGettingCampaignsList: 'Ocorreu um erro a obter as campanhas.'
}

var campaignTypesDescriptionI18N = {
		s1: 'Banner',
		s2: 'Merchandising',
		s3: 'Agente do mês',
		s4: 'Mensagem do dia',
		s3: 'Agente do mês',
		s3: 'Notícias',
		s3: 'Campanha',
		s3: 'Banner circular'
	};

var notificationTypesI18N = {
		balance: 'Saldo',
		terminal: 'Terminal',
		message: 'Mensagem',
		entity: 'Entidade',
		warning: 'Aviso',
		popup: 'Pop-up'
};
</script>

	</div>
	<div id="body">
		

<script type="text/javascript" src="js/login.js?21130"></script>


<div>

    








    

		<div id="loginDialog">
			<div>
				<form id="loginForm">
					<div>
						Introduza as suas credenciais<br/>
						<label>Utilizador</label>&nbsp;
						<input type="text" name="username" id="txtUsername"/> 
						<br/>
						<label>Palavra-passe</label>&nbsp;
						<input type="password" name="password" id="txtPassword"/> 
						<br/>

						<div class="left">Esqueci a Password </div>
						<div class="right"><input type="checkbox" name="forgotPassword" id="forgotPassword"/></div>
						<br/>
					</div>
				</form>
			</div>
		</div>
	
</div>

<script type="text/javascript">
$(function() {
	login.buildLoginDialogIA();
});
</script>



	</div>
	<div id="footer">
		


<!--BEGIN: FOOTER-->
<span>Desenvolvido por: <a href="http://www.wit-software.com">WIT Software, SA</a>&nbsp;&nbsp;&nbsp;&nbsp;v9.9.6</span>
<!--END: FOOTER-->
 
	</div>
</body>

</html>