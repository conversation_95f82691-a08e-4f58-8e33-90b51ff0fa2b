var geofencingMaps = {

	lastLocationLatitude: null, 
	lastLocationLongitude: null, 
	allSites: null,
	selectedSites: new Array(),
	
		
	buildInitialElements : function(lastLocationLatitude, lastLocationLongitude, allSites, tecGeofencedSites) {
		geofencingMaps.lastLocationLatitude = lastLocationLatitude;
		geofencingMaps.lastLocationLongitude = lastLocationLongitude;
		geofencingMaps.allSites = allSites;
		geofencingMaps.selectedSites = tecGeofencedSites;
		
		innerGenericMap.buildContainer(440, 340, $("#tecGeofencingMapContainer #map"), geofencingMaps.initContentAfterMapLoad);
	},
	
	initContentAfterMapLoad: function() {
		
		google.maps.visualRefresh = true;
		
		var mapElement = $("#tecGeofencingMapContainer #map")[0];
		
		googleMapsInterface.initMap(mapElement);
		googleMapsInterface.clearMarkers();

		// clear map listeners
		googleMapsInterface.markerSpiderfier.clearListeners('click');

		for (var i = 0; i < geofencingMaps.allSites.length; i++) {
			var row = geofencingMaps.allSites[i];
			
			var lat = row.latitude;
			var lng = row.longitude;
			
			var siteEnabled = false;
			var siteIcon = "images/mapIcons/marker_disabled.png";
			
			// Check if marker is enabled
			if ($.inArray(row.code, geofencingMaps.selectedSites) > -1) {
				siteEnabled = true;
				siteIcon = "images/mapIcons/marker_enabled.png";
			}
			
			// Compose marker
			var marker = new google.maps.Marker({
				position : new google.maps.LatLng(lat, lng),
				code: row.code,
				latitude: lat,
				longitude: lng,
				enabled: siteEnabled,
				icon: siteIcon
			});
			
			// Add marker
			googleMapsInterface.markers.push(marker);
			googleMapsInterface.markerSpiderfier.addMarker(marker);
		}

		googleMapsInterface.addMarkers();
		googleMapsInterface.adjustBounds();
		
		// Set marker click event
		googleMapsInterface.markerSpiderfier.addListener('click', function(marker, event) {
			// Toggle enable state
			marker.enabled = !marker.enabled;
			
			if(marker.enabled) {
				marker.icon = "images/mapIcons/marker_enabled.png";
			} else {
				marker.icon = "images/mapIcons/marker_disabled.png";
			}
			
			// Update map
			marker.setMap(googleMapsInterface.map);
			
			// Update tree
			tecs.updateRegionsTreeSingleMarker(marker);
		});
	},
	
	zoomToPoint: function(lat, lng, zoom) {
		var position = new google.maps.LatLng(lat, lng);
		googleMapsInterface.zoomToPoint(position, zoom);
	},
	
	addToSelectedSites : function(siteCode) {
		var alreadyRegistered = false;
		
		var id = $.inArray(siteCode, geofencingMaps.selectedSites);
		if(id > -1) {
			alreadyRegistered = true;
		}
		
		if(!alreadyRegistered) {
			geofencingMaps.selectedSites.push(siteCode);
			geofencingMaps.updateSelectedSites(siteCode, true);
		}
	},
	
	removeFromSelectedSites : function(siteCode) {
		var id = $.inArray(siteCode, geofencingMaps.selectedSites);
		if(id > -1) {
			geofencingMaps.selectedSites.splice(id, 1);
			geofencingMaps.updateSelectedSites(siteCode, false);
		}	
	},
	
	updateSelectedSitesWithBounds : function() {
		main.showOperationsLoading();
		
		var bounds = googleMapsInterface.map.getBounds();
		var markers = googleMapsInterface.markers.slice(0);

		console.time("updateSelectedSitesWithBounds");
		var interval = setInterval(function() {
			if(markers.length > 0) {
				var marker = markers.pop();

				if (bounds.contains(marker.position)) {
					marker.enabled = true;
				} else {
					marker.enabled = false;
				}

				// Update tree
				tecs.updateRegionsTreeSingleMarker(marker);
			} else {
				main.stopInterval(interval);
				console.timeEnd("updateSelectedSitesWithBounds");
				main.hideOperationsLoading();
			}
		}, 0);

		main.registerIntervalToStop(interval);

		if(geofencingMaps.selectedSites.length > 0) {
			$(".ui-dialog-buttonpane button:contains('"+ auxTexts.buttonApplyGeofencingText +"')").button("enable");
		} else {
			$(".ui-dialog-buttonpane button:contains('"+ auxTexts.buttonApplyGeofencingText +"')").button("disable");
		}
	},
	
	updateSelectedSites : function(siteCode, enabled) {
		var markers = googleMapsInterface.markers;
		var marker;
		
		var siteEnabled = false;
		var siteIcon = "images/mapIcons/marker_disabled.png";
		
		if (enabled) {
			siteEnabled = true;
			siteIcon = "images/mapIcons/marker_enabled.png";
		}
		
		for (var i = 0; i < markers.length; i++) {
			marker = markers[i];
			if (marker.code == siteCode) {
				
				marker.icon = siteIcon;
				marker.enabled = siteEnabled;
				
				marker.setMap(null);
				marker.setMap(googleMapsInterface.map);
			}
		}
	}

};
