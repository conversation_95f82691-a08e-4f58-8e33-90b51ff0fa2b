var search = {
	menuOptionsComponent : null,
	menuBtnComponent : null,
	applyFiltersBtnComponent : null,

	useDateSearch : false,
	fromDateSearchComponent : null,
	toDateSearchComponent : null,
	dateFieldName: null,
	dateSearchType: 'both',
	defaultDateInterval: null,
	
	threeStateComponents: null,
	useThreeStateFilters: false,
	
	currentSearchSource: null,
	
	//active search filters
	activeFilters: null,
	
	extraSearchFilters: new Object(),
	
	menuOptionsContentRealHeight: null,
	
	currentListIdentifier: null,
	
	personalSearchesList: new Array(),
	
	personalSearchesOptions: null,
	
	buildFiltersAndInitializeDataTable : function() {
		
		list.initDtConfigs();

		search.applyFiltersBtnComponent = $("#performSearchBtn");
		search.menuOptionsComponent = $("#configurationDialog");
		search.menuBtnComponent = $("#listConfigurationBtn");
		
		if(search.applyFiltersBtnComponent != null) {
			search.applyFiltersBtnComponent.button({
				text : false,
				icons : {
					primary : "ui-icon-search"
				}
			});
			
			search.applyFiltersBtnComponent.click(search.applyColumnsAndFilters);
		}
		
		search.threeStateComponents = $("#extraOptions div");
		if(search.threeStateComponents.length > 0) {
			search.useThreeStateFilters = true;
			search.threeStateComponents.buttonset();
			
			search.resetThreeStateFilters();
		}
		
		search.dateFieldName = $("#dateFilterName").val();
		if(search.dateFieldName && search.dateFieldName.length > 0) {
			search.useDateSearch = true;
			$("#predefinedIntervalComponent").hide();
			$("#customIntervalComponent").hide();
			
			$("#predefinedIntervalSelector").selectmenu({width: 120});
			if(search.defaultDateInterval != null) {
				$("#predefinedIntervalSelector").selectmenu("value", search.defaultDateInterval);
			}
			$("#predefinedIntervalSelector").change(search.intervalSelectorChange);
			search.drawCustomIntervalDateComponents();
			
		} else {
			search.useDateSearch = false;
		}
		
		search.updateSearchSource();
		
		list.buildDataTable();
	},
	
	updateSearchSource: function() {
		search.currentSearchSource = list.initialTableSource;
		
		search.validateThreeStateFilters();
		
		if (search.extraSearchFilters["fromDate"] == null) {
			search.validateStartDate();
		}
		if (search.extraSearchFilters["toDate"] == null) {
			search.validateEndDate();
		}
		
		search.currentSearchSource += search.validateExtraFilters();
		
	},
	
	updateDateColumnHeaderInfo: function() {

		$(list.tableComponent.fnSettings().aoColumns).each(function(colIndex) {
			if(this.sName == search.dateFieldName) {
				var th = $(".dataTables_scrollHead .dataTable").find("th:nth-child(" + (colIndex + 1) + ")");
				var div = th.find("div");
				div.find("span.ui-icon.ui-icon-heart").remove();
				div.append("<span class='ui-icon ui-icon-heart'></span>");
			}
		});
	},
	
	intervalSelectorChange: function() {
		var selectedPredefinedInterval = $("#predefinedIntervalSelector").selectmenu("value");
		
		if(selectedPredefinedInterval == "custom") {
			search.resetDateFilters();
			$("#customIntervalComponent").show();
		} else {
			$("#customIntervalComponent").hide();
		}
	},
	
	adjustCalandarStyles: function() {
		$("img.ui-datepicker-trigger").addClass("datePickerImg");
		$(".datePickerImg").removeAttr("alt");
		$(".datePickerImg").removeAttr("title");
		$(".datePickerImg").hover(function() {
			$(this).attr("src", "images/icons/datepicker_over.png");
		}, function() {
			$(this).attr("src", "images/icons/datepicker.png");
		});
		
		$("div.ui-datepicker-header.ui-widget-header.ui-helper-clearfix.ui-corner-all").removeClass("ui-corner-all");
	},
	
	drawCustomIntervalDateComponents: function() {
		search.fromDateSearchComponent = $("#searchFromDate");
		search.toDateSearchComponent = $("#searchToDate");
		if(search.useDateSearch && search.toDateSearchComponent.length <= 0) {
			search.dateSearchType = 'from';
		}
		if(search.useDateSearch && search.fromDateSearchComponent.length <= 0) {
			search.dateSearchType = 'to';
		}
		
		if(search.useDateSearch) {
			if(search.dateSearchType == 'both') {
				search.fromDateSearchComponent.datetimepicker({
					onClose : function(selectedDate) {
						search.toDateSearchComponent.datetimepicker("option", "minDate", selectedDate);
						search.adjustCalandarStyles();
					}
				});
				if(search.useDateSearch && search.toDateSearchComponent.length > 0) {
					search.toDateSearchComponent.datetimepicker({
						onClose: function( selectedDate ) {
							search.fromDateSearchComponent.datetimepicker("option", "maxDate", selectedDate);
							search.adjustCalandarStyles();
						}
					});
					main.registerDatepickerToDestroy("searchFromDate");
					main.registerDatepickerToDestroy("searchToDate");
				}
				$("#predefinedIntervalComponent").show();
				$("#customIntervalComponent").hide();
				
			} else if(search.dateSearchType == 'to'){
				search.toDateSearchComponent.datetimepicker({
					onClose: function( selectedDate ) {
						search.adjustCalandarStyles();
					}
				});
				main.registerDatepickerToDestroy("searchToDate");
				
				$("#predefinedIntervalComponent").hide();
				$("#customIntervalComponent").show();
				
			} else if(search.dateSearchType == 'from'){
				search.fromDateSearchComponent.datetimepicker({
					onClose : function(selectedDate) {
						search.adjustCalandarStyles();
					}
				});
				main.registerDatepickerToDestroy("searchFromDate");
				
				$("#predefinedIntervalComponent").hide();
				$("#customIntervalComponent").show();
			}
			
			search.resetDateFilters();
		}
	},

	resetDateFilters: function() {
		if(search.useDateSearch) {
			var fromDate = new Date();
			var toDate = new Date();
			
			var currentDateFilterValue = null;
			
			currentDateFilterValue = search.extraSearchFilters["fromDate"];
			if(currentDateFilterValue != null && currentDateFilterValue.length > 0) {
				fromDate = new Date(parseInt(currentDateFilterValue));
			}
			
			currentDateFilterValue = search.extraSearchFilters["toDate"];
			if(currentDateFilterValue != null && currentDateFilterValue.length > 0) {
				toDate = new Date(parseInt(currentDateFilterValue));
			}
			
			search.fromDateSearchComponent.datetimepicker("setDate", fromDate);
			search.toDateSearchComponent.datetimepicker("setDate", toDate);
			
			if(search.dateSearchType == 'both') {
				search.toDateSearchComponent.datetimepicker("option", "minDate", fromDate);
				search.fromDateSearchComponent.datetimepicker("option", "maxDate", toDate);
			} else if(search.dateSearchType == 'to') {
				search.toDateSearchComponent.datetimepicker("option", "maxDate", fromDate);
			} else if(search.dateSearchType == 'from') {
				search.fromDateSearchComponent.datetimepicker("option", "minDate", toDate);
			}
			
			search.adjustCalandarStyles();
		}
	},
	
	resetThreeStateFilters: function() {
		if(search.useThreeStateFilters) {
			search.threeStateComponents.each(function() {
				
				var checkedOption = $(this).find(":radio:checked");
				var name = checkedOption.attr("name");
				var value = search.extraSearchFilters[name];
				
				var newCheckedOption = $(this).find(":radio[value='"+value+"']");
				newCheckedOption.next().click();
			});
		}
	},
	
	
	resetColumns : function(activeColumns) {
		
		search.menuOptionsComponent.find("tr").each(function(){
			var tr = $(this);
			var checkbox = tr.find(".fieldName").find("input");
			var fieldName = checkbox.attr("id");

			if($.inArray(fieldName, activeColumns) == -1) {
				if(tr.hasClass("checked")) {
					checkbox.click();
				}
			} else {
				if(tr.hasClass("unchecked")) {
					checkbox.click();
				}
			}
		});
		
	},
	
	validateStartDate: function() {
		if(search.useDateSearch) {
			var selectedPredefinedInterval = $("#predefinedIntervalSelector").selectmenu("value");
			
			var date = new Date();
			
			if(selectedPredefinedInterval == "today") {
				date.setHours(0,0,0,0);
			} else if(selectedPredefinedInterval == "yesterday") {
				var yesterdayDate = date.getDate() - 1;
				date.setDate(yesterdayDate);
				date.setHours(0,0,0,0);
			} else if(selectedPredefinedInterval == "thisweek") {
				// in js, week starts at sunday, and our week starts at monday
				var firstDayOfWeekDate = date.getDate() - (date.getDay() - 1);
				date.setDate(firstDayOfWeekDate);
				date.setHours(0,0,0,0);
			} else if(selectedPredefinedInterval == "thismonth") {
				date.setDate(1);
				date.setHours(0,0,0,0);
			} else if(selectedPredefinedInterval == "lastmonth") {
				var last30DaysDate = date.getDate() - 30
				date.setDate(last30DaysDate);
				date.setHours(0,0,0,0);
			} else if(selectedPredefinedInterval == "lasthour") {
				date.setHours(date.getHours() - 1, date.getMinutes(), date.getSeconds(), date.getMilliseconds());
			} 

			if(search.dateSearchType != 'to') {
				if(selectedPredefinedInterval == "custom") {
					var startDate = search.fromDateSearchComponent.datetimepicker("getDate");

					if(startDate != null) {
						startDate.setSeconds(0,0);
						
						var startTimestamp = $.datepicker.formatDate('@', startDate);
						
						var currentDateFilterValue = search.extraSearchFilters["fromDate"];
						
						if(currentDateFilterValue == null || currentDateFilterValue.length == 0) {
							search.extraSearchFilters["fromDate"] = startTimestamp;
						}
					}
				} else {
					search.extraSearchFilters["fromDate"] = new Date(date).getTime();
				}
			}
		}
	},
	
	validateEndDate: function() {
		if(search.useDateSearch) {
			var selectedPredefinedInterval = $("#predefinedIntervalSelector").selectmenu("value");
			
			var date = new Date();
			
			if(selectedPredefinedInterval == "today" || 
					selectedPredefinedInterval == "thisweek" || 
					selectedPredefinedInterval == "thismonth" || 
					selectedPredefinedInterval == "lastmonth") {
				date.setHours(23,59,59,999);
			} else if(selectedPredefinedInterval == "yesterday") {
				var yesterdayDate = date.getDate() - 1;
				date.setDate(yesterdayDate);
				date.setHours(23,59,59,999);
			} 
			
			if(search.dateSearchType != 'from') {
				if(selectedPredefinedInterval == "custom") {
					var endDate = search.toDateSearchComponent.datetimepicker("getDate");
					endDate.setSeconds(59,999);
					
					if (endDate != null) {
						
						var endTimestamp = $.datepicker.formatDate('@', endDate);
		
						var currentDateFilterValue = search.extraSearchFilters["toDate"];
						
						if(currentDateFilterValue == null || currentDateFilterValue.length == 0) {
							search.extraSearchFilters["toDate"] = endTimestamp;
						}
					}

				} else {
					search.extraSearchFilters["toDate"] = new Date(date).getTime();
				}
			}
		}
	},
	
	validateThreeStateFilters: function() {
		if(search.useThreeStateFilters) {
			search.threeStateComponents.each(function() {
				var fieldId = $(this).attr("id");
				var checkedOption = $(this).find(":radio:checked");
				var value = checkedOption.val();
				var currentFilterValue = search.extraSearchFilters[fieldId];
				
				if((currentFilterValue == null || currentFilterValue.length == 0) && (value != null && value.length > 0)) {
					search.extraSearchFilters[fieldId] = value;
				}
			});
		}
	},
	
	//When we have extra filters that have nothing to do with table columns or predefined search, 
	//we need to put them in the search.extraSearchFilters array, 
	//and they will be automatically added to the search url
	validateExtraFilters: function() {
		var urlExtraParams = "";
		var extraFilters = Object.keys(search.extraSearchFilters);
		
		if(extraFilters.length > 0) {
			for(var i=0; i<extraFilters.length ; i++) {
				var key = extraFilters[i];
				urlExtraParams += key +"=" + encodeURIComponent(search.extraSearchFilters[key]) + "&";
			}
		}
		
		return urlExtraParams;
	},
	
	updateSearchConfigWithExtraFilters: function() {
		var extraFilters = Object.keys(search.extraSearchFilters);
		if(extraFilters.length > 0) {
			
			for(var i=0; i<extraFilters.length ; i++) {
				var key = extraFilters[i];
				var value = search.extraSearchFilters[key];
				
				// selectors for the input object of the wanted attribute
				var fieldNameElement = $("#fieldName[value='" + key + "']" );
				var fieldValueElement = fieldNameElement.next();
				
				var filterContent = fieldNameElement.parent().children(":not(input[type='hidden'])").first();
				
				// change the value for the search
				fieldValueElement.val(value);
				
				// add the value to the search input/select
				if(filterContent.is("select")) {
					filterContent.selectmenu("value", value);
				} else if (filterContent.hasClass("clearable")){
					var input = filterContent.find("input");
					input.val(value);
					// remove the watermark so it has the same appearence of the normal user input
					input.removeClass("watermark");
				}
			}
			
			search.resetThreeStateFilters();
//			search.resetDateFilters();

			search.extraSearchFilters = new Object();
		}
		
	},
	
	buildConfigurationMenu : function() {

		main.applyClearableStyles(search.menuOptionsComponent);
		
		search.menuBtnComponent.button({
			text : false,
			icons : {
				primary : "ui-icon-gear"
			}
		});
		
		
		search.menuOptionsComponent.dialog({
			dialogClass : "dialogAdvancedSearch",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			buttons : [
	           {text: auxTexts.buttonCleanText, click: search.clearFilters},
			   {text: auxTexts.buttonApplyText, click: search.applyColumnsAndFilters}
            ],
			open : function(event, ui) {
				search.menuOptionsContentRealHeight = $("#advancedSearchMenuContent table").height();

				if(search.menuOptionsContentRealHeight > main.maxConfigDialogHeight) {
					$("#advancedSearchMenuContent").height(main.maxConfigDialogHeight);
				}
				
				$('.ui-widget-overlay').unbind("click");
				$('.ui-widget-overlay').click(function() {
					search.menuOptionsComponent.dialog("close");
				});
				$('.ui-widget-overlay').addClass("withOpacity");
				
				search.menuOptionsComponent.keypress(function(event) {
					if (event.which == 13) {
						event.preventDefault();
						search.applyColumnsAndFilters();
					}
				});
			},
			close : function(event, ui) {
				$('.ui-widget-overlay').removeClass("withOpacity");
				search.menuOptionsComponent.unbind("keypress");
				search.menuBtnComponent.blur();
			}
		});
		
		main.registerConfigDialogToDestroy("configurationDialog");
		main.registerElementToDestroy(search.menuOptionsComponent);
		
		search.menuOptionsComponent.show();
		
		search.menuBtnComponent.click(function() {
			search.menuOptionsComponent.dialog("open");
		});

		var searchSelects = search.menuOptionsComponent.find("select");
		searchSelects.each(function() {
			var sel = $(this);
			var id = sel.attr("id");
			//$("#" + id).multiselect();
			
			$("#" + id).selectmenu({width: 278});
			main.registerSelectToDestroy(id);
		});

		search.menuOptionsComponent.find("tr").hover(function() {
			search.menuOptionsComponent.find("tr").removeClass("rowHover");
			$(this).addClass("rowHover");
		});

		search.menuOptionsComponent.find(".fieldName").click(function() {
			$(this).find("input").click();
		});
		
		search.menuOptionsComponent.find(".fieldName label").click(function() {
			$(this).parent().find("input").click();
			return false;
		});

		search.menuOptionsComponent.find(".fieldName input").change(search.updateChecked);
		
		search.menuOptionsComponent.find(".filterContent").children().change(search.updateFiltersContent);
	},
	
	clearFilters : function() {
		search.menuOptionsComponent.find(".filterContent").each(function() {
			var td = $(this);
			
			var filterContent = td.children(":not(input[type='hidden'])").first();
			if(filterContent.is("select")) {
				filterContent.selectmenu("index", 0);
			} else if (filterContent.hasClass("clearable")){
				var input = filterContent.find("input");
				input.val("");
				input.blur();
			}
			td.find("#fieldValue").val("");
		});
	},

	applyColumnsAndFilters: function() {
		search.menuOptionsComponent.dialog("close");
		var validFilters = search.configureFilters();
		var validColumns = search.configureColumns();
		if(validFilters && validColumns) {
			main.showTableLoading();
			search.applyConfigurations();
		} else {
			search.menuOptionsComponent.dialog("open");
		} 
	},
	
	configureColumns : function() {
		
		var tempActiveFieldsCounter = search.menuOptionsComponent.find("tr.checked").length;
		
		if(tempActiveFieldsCounter == 0) {
			main.hideTableLoading();
			main.showWarning(auxTexts.errorListFieldsEmptyText);
			return false;
		} else {
			
			var activeFields = search.menuOptionsComponent.find("tr.checked .fieldName");
			
			activeFields.each(function() {
				var td = $(this);
				var fieldName = td.find("label").attr("for");
				var colPosition = list.tableComponent.fnGetColumnIndex(fieldName);

				list.currentColumns[colPosition].bVisible = true;
			});
			
			var inactiveFields = search.menuOptionsComponent.find("tr.unchecked .fieldName");
			inactiveFields.each(function() {
				var td = $(this);
				var fieldName = td.find("input").attr("id");
				var colPosition = list.tableComponent.fnGetColumnIndex(fieldName);

				list.currentColumns[colPosition].bVisible = false;
			});
			
			return true;
		}
	},
	
	applyConfigurations : function() {

		if(list.tableComponent != null) {
			list.defaultSort = list.tableComponent.fnSettings().aaSorting;
			list.tableComponent.fnDestroy(true);
			list.createTable();
		}
	},
	
	configureFilters: function() {
		search.activeFilters = search.menuOptionsComponent.find("tr.checked").find(".filterContent");
		
		search.updateSearchSource();
		
		var tempSearchSource = search.currentSearchSource;
		var validFilters = true;
		search.activeFilters.each(function(index) {
			
			var td = $(this);
			var fieldValue = td.find("#fieldValue").val();
			var fieldName = td.find("#fieldName").val();
			var validCurrentFilter = true;
			
			var regex = td.find("#fieldRegex").val();
			var input = td.find("input:not([type='hidden'])");

			td.removeClass("invalidValue");
			input.parent().removeClass("invalidValue");
			input.removeClass("invalidValue");
			
			if(fieldValue != null && fieldValue.length > 0) {

				if(regex != null && regex.length > 0) {
					if(!main.validateInput(fieldValue, regex)) {
						td.addClass("invalidValue");
						input.parent().addClass("invalidValue");
						input.addClass("invalidValue");
						
						search.currentSearchSource = tempSearchSource;
						main.showWarning(auxTexts.errorValidateFiltersText);
						validFilters = false;
						validCurrentFilter = false;
					}
				}
				
				if(validCurrentFilter == true) {
					var columnType = td.find("#fieldName").attr("tyepename");
					if(columnType == "units") {
						fieldValue = main.convertExternalUnitsToInternalUnits(main.getUnformatedValue(fieldValue));
					}

					search.currentSearchSource += fieldName + "=" + encodeURIComponent(fieldValue) + "&";
				}
			}

		});
		
		if(!validFilters) {
			main.hideTableLoading();
			main.showWarning(main.invalidFiltersText);
		}
		
		return validFilters;
	},
	
	updateFiltersContent: function(event) {
		var input = $(event.target);
		var td = input.parent().parent();
			
		td.find("#fieldValue").val(input.val());
	},

	updateChecked : function(event) {
		var input = event.target;
		var tr = $(input).parent().parent();
		var hasCheckedClass = tr.hasClass("checked");
		
		tr.removeClass();

		var filterContent = tr.find(".filterContent").children().first();
		
		if (hasCheckedClass) {
			tr.addClass("unchecked");
			if(filterContent.is("select")) {
				filterContent.selectmenu("index", 0);
				filterContent.selectmenu("disable");
			} else {
				filterContent.attr("disabled", "disabled");
				filterContent.children().attr("disabled", "disabled");
			}
		} else {
			tr.addClass("checked");
			if(filterContent.is("select")) {
				filterContent.selectmenu("enable");
			} else {
				filterContent.removeAttr("disabled");
				filterContent.children().removeAttr("disabled");
			}
			main.focusLater(filterContent);
		}
	},
	
	addExtraInputComponent: function(componentId, title, inputType) {
		var extraInputHtml = "<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
		
		if(inputType == 'button') {
			extraInputHtml += "<button id='" + componentId + "' type='button' title='" + title + "'></button>";
		} else {
			extraInputHtml += "<input id='" + componentId + "' type='" + inputType + "' />";
			extraInputHtml += "<label for='" + componentId + "' style='width: 25px; height: 23px;' title='" + title + "'></label>";
		}
		extraInputHtml += "</td>";
		
		$("#extraOptions tr:last-of-type").append(extraInputHtml);
	},
	
	addExtraComponent: function(componentHtml) {
		var extraComponentHtml = "<td>" + componentHtml + "</td>";
		$("#extraOptions tr:last-of-type").append(extraComponentHtml);
	},
	
	parseExtraFilters: function(urlParams) {
		var trimedParams = urlParams.split("?", 2)[1];
		
		if (trimedParams.length == 0) {
			return;
		}
		
		var filters = trimedParams.split("&");
		for (var i = 0; i < filters.length; i++) {
			var key = filters[i].split("=", 2)[0];
			var value = filters[i].split("=", 2)[1];

			if (key.length > 0 && value.length > 0) {
				search.extraSearchFilters[key] = value;
			}
		}
	},
	
	buildPersonalSearchesComponent: function(listId) {
		search.currentListIdentifier = listId;
		
		search.personalSearchesOptions = $("#personalSearchesOptions");
		search.getPersonalSearches();
		search.buildCreatePersonalSearchComponent();
		search.buildRemovePersonalSearchComponent();
		search.buildEditPersonalSearchComponent();
	},
	
	buildPersonalSearchesOptionsComponent: function(selectPersonalSearch) {
		search.personalSearchesOptions.unbind("change");
		search.personalSearchesOptions.find("option[value^=ps_]").each(function(index) {
			$(this).remove();
		});
		
		var searchesCount = search.personalSearchesList.length;
		
		// Hide controls if there is no personal searchs
		if(searchesCount == 0) {
			search.personalSearchesOptions.find("option[value='remove']").addClass("ui-state-hidden");
			search.personalSearchesOptions.find("option[value='edit']").addClass("ui-state-hidden");
		} else {
			// Hide controls if there are no selected personal searchs
			if (search.currentPersonalSearch == null) {
				search.personalSearchesOptions.find("option[value='remove']").addClass("ui-state-hidden");
				search.personalSearchesOptions.find("option[value='edit']").addClass("ui-state-hidden");
			} else {
				search.personalSearchesOptions.find("option[value='remove']").removeClass("ui-state-hidden");
				search.personalSearchesOptions.find("option[value='edit']").removeClass("ui-state-hidden");
			}

			var selectPersonalSearchIndex = null;
			
			for(var i = 0; i < searchesCount; i++) {
				var pSearch = search.personalSearchesList[i];

				if (selectPersonalSearch == pSearch.searchName) {
					selectPersonalSearchIndex = i;
				}
				
				search.personalSearchesOptions.append("<option value='ps_" + i + "'>" + auxTexts.loadPersonalSearch.replace("[0]", pSearch.searchName) + "</option>");
			}
			
			if (selectPersonalSearchIndex != null) {
				search.personalSearchesOptions.selectmenu("value", "ps_" + selectPersonalSearchIndex);
			}
		}
		
		search.personalSearchesOptions.unbind("change").change(search.onChangePersonalSearchesOption);
		
		search.personalSearchesOptions.selectmenu({width: 300});
		main.registerSelectToDestroy("personalSearchesOptions");
	},
	
	buildCreatePersonalSearchComponent: function() {
		$("#confirmPersonalSearchName").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			open : function(event, ui) {
				$('.ui-widget-overlay').addClass("withOpacity");
			},
			close : function(event, ui) {
				$('.ui-widget-overlay').removeClass("withOpacity");
			}
		});
		
		main.registerConfigDialogToDestroy("confirmPersonalSearchName");
		main.registerElementToDestroy($("#confirmPersonalSearchName"));
		
		$("#confirmPersonalSearchName button").button();
		
		$("#confirmSearchNameCancelBtn").unbind("click").click(search.closeCreatePersonalSearchDialog);
		$("#confirmSearchNameOkBtn").unbind("click").click(search.createPersonalSearch);
	},
	
	closeCreatePersonalSearchDialog: function() {
		$("#confirmPersonalSearchName").dialog("close");
		search.currentPersonalSearch = null;
		search.buildPersonalSearchesOptionsComponent(null);
		search.personalSearchesOptions.selectmenu("value", "options");
	},
	
	openCreatePersonalSearchDialog: function() {
		$("#personalSearchName").val("");
		$("#confirmPersonalSearchName").dialog("open");
		main.focusLater($("#personalSearchName"));
	},
	
	buildRemovePersonalSearchComponent: function() {
		$("#confirmRemovePersonalSearch").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			open : function(event, ui) {
				$('.ui-widget-overlay').addClass("withOpacity");
			},
			close : function(event, ui) {
				$('.ui-widget-overlay').removeClass("withOpacity");
			}
		});
		
		main.registerConfigDialogToDestroy("confirmRemovePersonalSearch");
		main.registerElementToDestroy($("#confirmRemovePersonalSearch"));
		
		$("#confirmRemovePersonalSearch button").button();
		
		$("#confirmRemoveSearchCancelBtn").unbind("click").click(search.closeRemovePersonalSearchDialog);
		$("#confirmRemoveSearchOkBtn").unbind("click").click(search.removePersonalSearch);
	},
	
	closeRemovePersonalSearchDialog: function() {
		$("#confirmRemovePersonalSearch").dialog("close");
		search.buildPersonalSearchesOptionsComponent(search.currentPersonalSearch.searchName);
	},
	
	openRemovePersonalSearchDialog: function() {
		$("#confirmRemovePersonalSearch").dialog("open");
	},
	
	buildEditPersonalSearchComponent: function() {
		$("#confirmEditPersonalSearch").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			open : function(event, ui) {
				$('.ui-widget-overlay').addClass("withOpacity");
			},
			close : function(event, ui) {
				$('.ui-widget-overlay').removeClass("withOpacity");
			}
		});
		
		main.registerConfigDialogToDestroy("confirmEditPersonalSearch");
		main.registerElementToDestroy($("#confirmEditPersonalSearch"));
		
		$("#confirmEditPersonalSearch button").button();
		
		$("#confirmEditSearchCancelBtn").unbind("click").click(search.closeEditPersonalSearchDialog);
		$("#confirmEditSearchOkBtn").unbind("click").click(search.editPersonalSearch);
	},
	
	closeEditPersonalSearchDialog: function() {
		$("#confirmEditPersonalSearch").dialog("close");
		search.buildPersonalSearchesOptionsComponent(search.currentPersonalSearch.searchName);
	},
	
	openEditPersonalSearchDialog: function() {
		$("#confirmEditPersonalSearch").dialog("open");
	},
	
	onChangePersonalSearchesOption: function() {
		var selectedOption = this.value;
		
		if(selectedOption == "create") {
			search.openCreatePersonalSearchDialog();
		} else if(selectedOption == "edit") {
			search.openEditPersonalSearchDialog();
		} else if(selectedOption == "remove") {
			search.openRemovePersonalSearchDialog();
		} else if(selectedOption == "options") {
			search.currentPersonalSearch = null;
			search.buildPersonalSearchesOptionsComponent("options");
		} else {
			var index = parseInt(selectedOption.replace("ps_", ""));
			search.currentPersonalSearch = search.personalSearchesList[index];
			search.applyPersonalSearchParams();
			search.buildPersonalSearchesOptionsComponent(search.currentPersonalSearch.searchName);
		}
	},
	
	applyPersonalSearchParams: function() {
		var searchParams = search.currentPersonalSearch.searchParameters;
		
		search.parseSearchParamsAndApply(searchParams);
		
	},
	
	getConfigurationsForPersonalSearch: function() {
		var searchOptions = "searchConfiguration=";
		
		if(search.useThreeStateFilters) {
			search.threeStateComponents.each(function() {
				var fieldId = $(this).attr("id");
				var checkedOption = $(this).find(":radio:checked");
				var value = checkedOption.val();
				searchOptions += "[" + fieldId + "|" + value + "]";
			});
		}
		
		if(search.useDateSearch) {
			var selectedPredefinedInterval = $("#predefinedIntervalSelector").selectmenu("value");
			if(selectedPredefinedInterval == "custom") {
				if(search.dateSearchType != 'to') {
					var startDate = search.fromDateSearchComponent.datetimepicker("getDate");
					
					if(startDate != null) {
						var startTimestamp = $.datepicker.formatDate('@', startDate);
						
						searchOptions += "[fromDate|" + startTimestamp + "]";
					}
				}
				if(search.dateSearchType != 'from') {
					var endDate = search.toDateSearchComponent.datetimepicker("getDate");
		
					if (endDate != null) {
						var endTimestamp = $.datepicker.formatDate('@', endDate);
						
						searchOptions += "[toDate|" + endTimestamp + "]";
					}
				}
			} else {
				searchOptions += "[date_interval|" + selectedPredefinedInterval + "]";
			}
		}
		
		search.activeFilters.each(function() {
			var filter = $(this);
			var name = filter.find("#fieldName").val();
			var value = filter.find("#fieldValue").val();
			
			searchOptions += "[" + name + "|" + value + "]";
			
		});
		
		return searchOptions;
	},
	
	createPersonalSearch: function() {
		
		var searchName = $("#personalSearchName").val();
		var searchNameRegex = /^[a-zA-z0-9_ ]{1,50}$/;
		
		if (!main.validateInput(searchName, searchNameRegex)) {
			main.showWarning(auxTexts.invalidSearchName);
			return;
		}
		
		var searchOptions = search.getConfigurationsForPersonalSearch();
		
		searchOptions += "&searchName=" + searchName + "&listIdentifier=" + search.currentListIdentifier;
		
		$.ajax({
			type: "POST",
			dataType: "json",
			cache: false,
			data: searchOptions,
			url: "CreatePersonalSearch",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					main.showSuccess(response.statusMessage);
					search.closeCreatePersonalSearchDialog();
					search.getPersonalSearches(searchName);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
				
			}
		});
	},
	
	removePersonalSearch: function() {
		
		var searchName = search.currentPersonalSearch.searchName;
		$.ajax({
			type: "POST",
			dataType: "json",
			cache: false,
			url: "RemovePersonalSearch?listIdentifier=" + search.currentListIdentifier + "&searchName=" + searchName,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					main.showSuccess(response.statusMessage);
					search.closeRemovePersonalSearchDialog();
					search.personalSearchesOptions.selectmenu("value", "options");
					search.getPersonalSearches();
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
				
			}
		});
	},
	
	getPersonalSearches: function(selectPersonalSearch) {
		$.ajax({
			type: "GET",
			dataType: "json",
			cache: false,
			url: "PersonalSearchesPopulate?listIdentifier=" + search.currentListIdentifier,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
												
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					search.personalSearchesList = response.aaData;
					
					// Set current personal search
					for(var i = 0; i < search.personalSearchesList.length; i++) {
						if (selectPersonalSearch == search.personalSearchesList[i].searchName) {
							search.currentPersonalSearch = search.personalSearchesList[i];
						}
					}
					
					search.buildPersonalSearchesOptionsComponent(selectPersonalSearch);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
				
			}
		});
	},
	
	editPersonalSearch: function() {
		
		var searchName = search.currentPersonalSearch.searchName;

		var searchOptions = search.getConfigurationsForPersonalSearch();
		
		searchOptions += "&searchName=" + searchName + "&listIdentifier=" + search.currentListIdentifier;
		
		$.ajax({
			type: "POST",
			dataType: "json",
			cache: false,
			data: searchOptions,
			url: "EditPersonalSearch",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					main.showSuccess(response.statusMessage);
					search.closeEditPersonalSearchDialog();
					search.getPersonalSearches(searchName);
					search.buildPersonalSearchesOptionsComponent(search.currentPersonalSearch.searchName);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
				
			}
		});
	},
	
	applySearchParamsOnBrowserNavigation: function(parameters) {
		var searchParams = parameters;
		
		search.extraSearchFilters = new Object();
		var activeColumns = new Array();

		var searchParamsSplit = searchParams.split("&");
		
		for(var i=0 ; i<searchParamsSplit.length ; i++) {
			var paramSplit = searchParamsSplit[i].split("=", 2);
			var key = paramSplit[0];
			var value = paramSplit[1];
			
			if (key.length === 0) {
				continue;
			}
			
			if(key == "date_interval") {
				$("#predefinedIntervalSelector").selectmenu("value", value);
			} else if(key == "fromDate" || key == "toDate") {
				$("#predefinedIntervalSelector").selectmenu("value", "custom");
				search.resetDateFilters();
			}
			
			search.extraSearchFilters[key] = value;
			
			activeColumns.push(key);
		};

		search.clearFilters();
		search.resetColumns(activeColumns);
		search.updateSearchConfigWithExtraFilters();
		search.applyColumnsAndFilters();
		
	},
	
	parseSearchParamsAndApply: function(searchParams) {
		
		search.extraSearchFilters = new Object();
		var activeColumns = new Array();

		var searchParamsSplit = searchParams.substring(1, searchParams.length - 1).split("][");
		
		for(var i=0 ; i<searchParamsSplit.length ; i++) {
			var paramSplit = searchParamsSplit[i].split("|", 2);
			var key = paramSplit[0];
			var value = paramSplit[1];
			
			if(key == "date_interval") {
				$("#predefinedIntervalSelector").selectmenu("value", value);
			} else if(key == "fromDate" || key == "toDate") {
				$("#predefinedIntervalSelector").selectmenu("value", "custom");
				search.resetDateFilters();
			}
			
			search.extraSearchFilters[key] = value;
			
			activeColumns.push(key);
		};

		search.clearFilters();
		search.resetColumns(activeColumns);
		search.updateSearchConfigWithExtraFilters();
		search.applyColumnsAndFilters();
		
	},

	resizeContent: function() {
		if(main.maxConfigDialogHeight < search.menuOptionsContentRealHeight) {
			$("#advancedSearchMenuContent").height(main.maxConfigDialogHeight);
		} else {
			$("#advancedSearchMenuContent").height(search.menuOptionsContentRealHeight);
		}
		if(search.menuOptionsComponent != null && search.menuOptionsComponent.hasClass('ui-dialog-content')) {
			search.menuOptionsComponent.dialog("option", "position", "center");
		}
	}
};

$(window).resize(search.resizeContent);
