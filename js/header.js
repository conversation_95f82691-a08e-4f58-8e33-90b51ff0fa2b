var header = {
	menuLiMapping: [],
	menuTdMapping: [],
	
	currentOpenMenu: null,
	nextMenuToOpen: null,
	
	currentSelectedMenuButton: null,
	
	ignoreHashChange: false,
	oldUrlforIE: null,
	applyFiltersAfterRedirect: false,
	filtersToBeAppliedAfterRedirect: null,
	isUrlBrowserNavigation: false,

	isRedirect: false,
	
	buildMenus: function() {
		main.language = $("#language").val();

		header.buildChangePassComponent();
		
		header.buildUserMenu();
		loadExternals.loadExternalModules();
		header.buildGlobalNavMenus();
		
		
		$("html").click(function() {
			if(header.currentOpenMenu != null) {
				header.currentOpenMenu.dialog("close");
			}
			
			if(header.openUserMenu != null) {
				header.openUserMenu.dialog("close");
			}
		});
		

		window.onhashchange = function(event) {
			
			var oldActionUrl = "";
			if (event.oldURL == null) {
				if (header.oldUrlforIE != null) {
					oldActionUrl = header.oldUrlforIE.slice(header.oldUrlforIE.indexOf("#"));
				} 
				
			} else {
				oldActionUrl = event.oldURL.slice(event.oldURL.indexOf("#"));
			}
			
			var newActionUrlDecoded = Base64.decode(location.hash.replace("#", ""));
			
			//check if browser is IE
			var ua = window.navigator.userAgent;
			var ms_ie = ~ua.indexOf('MSIE ') || ~ua.indexOf('Trident/');
			
			if (ms_ie === 0) {
				if (event.oldURL.indexOf("#") < 0) {
					header.ignoreHashChange = true;
				}
			} else {
				if (oldActionUrl == "" || location.hash == oldActionUrl) {
					header.ignoreHashChange = true;
				}
			}
			
			if (header.ignoreHashChange) {
				header.ignoreHashChange = false;
				return;
			}
			header.ignoreHashChange = false;

			var newAction = newActionUrlDecoded.split("?")[0];
			var parameters = newActionUrlDecoded.split("?")[1];
			
			// check if new action is the same as before and
			// just update the URL, do not redirect
			if (newAction.replace("Populate", "") == Base64.decode(oldActionUrl.replace("#", "")).replace("Populate", "").split("?")[0]) {

				//if it has Populate in the name, no need to redirect, just update the search source (second and further calls on a list view come here)
				if (newActionUrlDecoded.indexOf("Populate") != -1) {
					
					search.getConfigurationsForPersonalSearch();
					
					if (parameters != null && parameters.indexOf("searchConfiguration") >= 0) {
						var tmp = parameters.slice(parameters.indexOf("searchConfiguration")+20);
						
						search.parseSearchParamsAndApply(tmp);
					} else {
						search.applySearchParamsOnBrowserNavigation(parameters);
					}
					
				} else { //otherwise redirect to the action page with clean filters (usually this is the first page loaded on a list view)
					//Not sure if this is happening at the moment ? 
					//would be nice to do this without the redirect...
					
					header.redirectToPage(newAction);
					header.ignoreHashChange = false;
				}
				
			} else {
				//if action is not the same as before, redirect to the new page and apply filters if necessary
				//FIXME PlatformTecCommStatsListOfMessages this is not working
				if (parameters != null) {
					
					search.extraSearchFilters = new Object();
					
					var parametersArray = parameters.split("&");
					for (var index = 0; index < parametersArray.length; ++index) {
					    if (parametersArray[index].split("=")[0].length > 0) {
					    	
					    	//searchConfiguration is in the url but it is not an extraSearchFilter
					    	if (parametersArray[index].split("=")[0] == "searchConfiguration") {
					    		
					    		header.filtersToBeAppliedAfterRedirect = parametersArray[index];
								header.applyFiltersAfterRedirect = true;
								//search.parseSearchParamsAndApply(tmp);
					    	} else {
					    		search.extraSearchFilters[parametersArray[index].split("=")[0]] = parametersArray[index].split("=")[1];
					    	}
					    	
					    	//Special cases that page action name does not match ajax request call name minus Populate
					    	if (newAction === "PopulateEntitiesSubTree" || newAction === "PopulateEntitiesChildren") {
					    		newAction = "EntitiesTree";
					    	} else if (newAction === "SearchEntitiesTree") {
					    		main.treeSearchValue = parametersArray[index].split("=")[1];
					    		newAction = "EntitiesTree";
					    	} else if (newAction === "ProductStockPopulate") {
					    		newAction = "ProductStockList";
//					    	} else if (newAction === "PlatformTecCommStatsListOfMessages") {
//					    		newAction = "PlatformTecCommStats";
					    	}
					    	
					    }
					}
					
				} 
				header.isUrlBrowserNavigation = true;
				header.redirectToPage(newAction.replace("Populate", ""));
				
			}

		};
	}, 
	
	buildGlobalNavMenus: function() {
		
		$(".js-globalNavMenu").each(function(index) {
			header.buildGlobalNavMenuEntry($(this));
		});
		
		var menuWidth = $(".globalNav").width();
		
		if(menuWidth > main.minWidth) {
			main.minWidth = menuWidth;
		}
		
		$("#body").css("min-width", main.minWidth + "px");
		$("#footer").css("min-width", main.minWidth + "px");
		$("#header").css("min-width", main.minWidth + "px");
	},

	buildGlobalNavMenuEntry: function(menu) {
		// The <td> element
		var menuButton = menu;
		if (!menu[0].classList.contains('js-noSubMenu')) {
			menuButton = menu.parent();	
		}
		
		var isNoSubmenuItem = menuButton[0].classList.contains('js-noSubMenu');
				
		menuButton.click(function(event) {
			event.stopPropagation();
			if (isNoSubmenuItem) {
				// Fix to prevent having submenu opened when access to  campaigns menu
				if ($('.globalNavActive').length > 1 ) {
					header.currentOpenMenu.dialog("close");
				}
			} else {			
				if(!menu.dialog("isOpen")) {
					
					$(".globalNav td").removeClass("globalNavActive");
					if(header.currentSelectedMenuButton != null) {
						header.currentSelectedMenuButton.addClass("globalNavActive");
					}
					menuButton.addClass("globalNavActive");
	
					if(header.currentOpenMenu == null) {
						menu.dialog("open");
					} else {
						header.nextMenuToOpen = menu;
						header.currentOpenMenu.dialog("close");
					}
				} else {
					header.currentOpenMenu && header.currentOpenMenu.dialog("close");
				}
			}
		});
		
		if(!isNoSubmenuItem) {
			menu.dialog({
				dialogClass : "globalNavDialogNoTitle",
				draggable : false,
				resizable : false,
				width : 210,
				minHeight : 10,
				position : {
					my : "left top",
					at : "left+2 bottom-6",
					of : menuButton
				},
				modal : false,
				autoOpen : false,
				open : function(event, ui) {
					header.currentOpenMenu = menu;
				},
				close: function(event, ui) {
					header.currentOpenMenu = null;
					
					if(header.nextMenuToOpen != null) {
						header.nextMenuToOpen.dialog("open");
						header.nextMenuToOpen = null;
					} else {
						$(".globalNav td").removeClass("globalNavActive");
						if(header.currentSelectedMenuButton != null) {
							header.currentSelectedMenuButton.addClass("globalNavActive");
						}
					}
				},
				show : {
					effect : "fade",
					duration : 200
				},
				hide : {
					effect : "fade",
					duration : 200
				}
				
			});
		}
		
		//We have to force width, otherwise the header will break
		header.resizeMenu();
	
		if(!isNoSubmenuItem) {
			menu.find("li").each(function() {
				var li = $(this);
				
				header.addClickActionToMenuItem(li, menuButton);
				
			});
		} else {
			header.addClickActionToMainMenuItem(menuButton);
		}
		
		menu.show();
		
	},
	
	resizeMenu: function() {
		var menuBtnsCount = $(".globalNavButton").length;
		$(".globalNav").width(menuBtnsCount*149 + 216);
	},
	
	
	
	addClickActionToMainMenuItem: function(td) {
		var menuButton = td;
		
		/*if(td != null) {
			menuButton = td;
		}*/
		
		// var menu = li.closest("ul");
		
		var a = menuButton.find("a");
		var actionUrl = a.attr("href");
		//header.menuLiMapping[actionUrl] = li;
		header.menuTdMapping[actionUrl] = menuButton;
		
		td.click(function() {
			if(header.currentSelectedMenuButton != null) {
				header.currentSelectedMenuButton.removeClass("globalNavActive");
			}
			header.currentSelectedMenuButton = menuButton;
			
			//shouldn't use this here because it adds unnecessary steps on the browser history BUT
			//those pages that don't have a populate call need to get updated on the url hash, 
			//so they have to be introduced here manually (or find a better way/place to do this) 
			if (Base64.decode(window.location.hash) != actionUrl) {
				if ((actionUrl === "ReferenceData" || actionUrl === "SystemMonitoring" || actionUrl === "SystemProperties"
					|| actionUrl === "PlatformTecCommStats" || actionUrl === "PlatformTecMessages" || actionUrl === "ClientRegistration") && !header.isUrlBrowserNavigation) {
					header.ignoreHashChange = true;
					
					window.location.hash = Base64.encode(actionUrl);
					header.oldUrlforIE = window.location.hash;
				}
			}
			header.isUrlBrowserNavigation = false;
			// menu.dialog("close");
			
			$.ajax({
				type: "GET",
				url: actionUrl,
				beforeSend: function() {
					main.showLoading();
				},
				success: function(response) {
					if(main.isSessionExpired(response)) {
						return;
					} 
						
					header.destroyElementsAndCleanVars();
					$("#body").html(response);
					
				},
				error: function(jqXHR, status, error) {
					main.showWarning(auxTexts.errorGeneric);
				}
			});
			
			$(".globalNavMenu li").removeClass("selectedMenuItem");
			$(this).addClass("globalNavActive");
			
			return false;
		});
	},

	
	
	addClickActionToMenuItem: function(li, td) {
		var menuButton = li.closest("td");
		
		if(td != null) {
			menuButton = td;
		}
		
		var menu = li.closest("ul");
		
		var a = li.find("a");
		var actionUrl = a.attr("href");
		header.menuLiMapping[actionUrl] = li;
		header.menuTdMapping[actionUrl] = menuButton;
		
		li.click(function() {
			if(header.currentSelectedMenuButton != null) {
				header.currentSelectedMenuButton.removeClass("globalNavActive");
			}
			header.currentSelectedMenuButton = menuButton;
			
			//shouldn't use this here because it adds unnecessary steps on the browser history BUT
			//those pages that don't have a populate call need to get updated on the url hash, 
			//so they have to be introduced here manually (or find a better way/place to do this) 
			if (Base64.decode(window.location.hash) != actionUrl) {
				if ((actionUrl === "ReferenceData" || actionUrl === "SystemMonitoring" || actionUrl === "SystemProperties"
					|| actionUrl === "PlatformTecCommStats" || actionUrl === "PlatformTecMessages" || actionUrl === "ClientRegistration") && !header.isUrlBrowserNavigation) {
					header.ignoreHashChange = true;
					
					window.location.hash = Base64.encode(actionUrl);
					header.oldUrlforIE = window.location.hash;
				}
			}
			header.isUrlBrowserNavigation = false;
			menu.dialog("close");
			
			$.ajax({
				type: "GET",
				url: actionUrl,
				beforeSend: function() {
					main.showLoading();
				},
				success: function(response) {
					if(main.isSessionExpired(response)) {
						return;
					} 
						
					header.destroyElementsAndCleanVars();
					$("#body").html(response);
					
				},
				error: function(jqXHR, status, error) {
					main.showWarning(auxTexts.errorGeneric);
				}
			});
			
			$(".globalNavMenu li").removeClass("selectedMenuItem");
			$(this).addClass("selectedMenuItem");
			
			return false;
		});
	},
	
	destroyElementsAndCleanVars: function() {
		if(!header.isRedirect) {
			search.extraSearchFilters = new Object();
		}
		header.isRedirect = false;
		header.currentOpenMenu = null;
		header.nextMenuToOpen = null;

		list.tableComponent=null;
		list.tableContainerHeight = null;
		list.successCallback = null;
		list.sumColumns = new Array();
		list.sumColumnsFormatters = new Array();
		list.defaultSort = [];

		search.activeFilters = null;
		search.fromDateSearchComponent = null;
		search.toDateSearchComponent = null;
		search.dateFieldName = null;
		search.useDateSearch = false;
		search.dateSearchType = 'both';
		search.currentListIdentifier = null;
		search.currentPersonalSearch = null;
		search.defaultDateInterval = null;
		
		details.detailsComponent = null;
		details.optionsSelectComponent = null;
		details.entityIdName = null;
		details.entityIdValue = null;
		details.entityData = null;
		details.entityName = null;
		details.entityContextOptionsMapping = null;
		details.executeAfterFillDetailsScreen = null;
		details.ptionSelectChange = null;
		details.subEntityContextOptionsMapping = null;
		details.subEntityData = null;
		details.subEntityName = null;
		details.entityIdentifier = null;
		details.successCallback = null;
		
		main.stopTimers();
		main.stopIntervals();
		main.destroySelects();
		main.destroyDatepickers();
		main.destroyDetailsDialogs();
		main.destroyConfigsDialogs();
		main.destroyRegisteredElements();
		main.destroyInnerComponents();
	},
	
	buildUserMenu: function() {
		var menu = $(".userMenu");
		var menuButton = $(".userMenuBtn");
		
		menu.dialog({
			dialogClass : "dialogNoTitle",
			draggable : false,
			resizable : false,
			width : 170,
			minHeight : 10,
			position : {
				my : "right top",
				at : "right-1 bottom",
				of : menuButton
			},
			modal : false,
			autoOpen : false,
			open : function(event, ui) {
				header.currentOpenMenu = menu;
				$(".globalNav td").removeClass("globalNavActive");
				header.currentSelectedMenuButton.addClass("globalNavActive");
			},
			close : function(event, ui) {
				header.currentOpenMenu = null;
				
				if(header.nextMenuToOpen != null) {
					header.nextMenuToOpen.dialog("open");
					header.nextMenuToOpen = null;
				} 
			},
			show : {
				effect : "fade",
				duration : 200
			},
			hide : {
				effect : "fade",
				duration : 200
			}
		});
		
		menuButton.click(function(event) {
			event.stopPropagation();
			if(!menu.dialog("isOpen")) {
				if(header.currentOpenMenu == null) {
					menu.dialog("open");
				} else {
					header.nextMenuToOpen = menu;
					header.currentOpenMenu.dialog("close");
				}
			} else {
				header.currentOpenMenu.dialog("close");
			}
		});
		
		menu.find("li").click(function() {
			var li = $(this);
			var a = li.find("a");
			
			var href = a.attr("href");
			
			if(href.indexOf("Logout") != -1) {
				$.ajax({
					type: "GET",
					url: href + "?showMessage=false",
					complete: function() {
						window.location="SIRE";
					}
				});
				return false;
			} else if (href.indexOf("ChangePassword") != -1) {
				if(header.currentOpenMenu != null) {
					header.currentOpenMenu.dialog("close");
				}
				$("#changePasswordConfirm").dialog("open");
				return false;
			}
		});
		
		menu.show();
	},
	
	
	buildChangePassComponent: function() {
		var txtCurrentPass = $("#currentPass");
		var txtNewPass = $("#newPass");
		var txtConfirmNewPass = $("#confirmNewPass");
		
		$("#changePasswordConfirm").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 370,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			open : function(event, ui) {
				$('.ui-widget-overlay').addClass("withOpacity");
				main.focusLater(txtCurrentPass);
			},
			close : function(event, ui) {
				$('.ui-widget-overlay').removeClass("withOpacity");
				$("#changePasswordConfirm input").val("");
			}
		});
		
		$("#changePasswordConfirm button").button();
		
		$("#confirmChangePassCancelBtn").unbind("click").click(header.closeChangePassDialog);
		$("#confirmChangePassOkBtn").unbind("click").click(header.changePasswordForUser);

		txtNewPass.keypress(function(event) {
			if (event.which == 13 && txtNewPass.val() != "") {
				event.preventDefault();
				main.focusLater(txtConfirmNewPass);
			}
		});

		txtConfirmNewPass.keypress(function(event) {
			if (event.which == 13 && txtConfirmNewPass.val() != "") {
				event.preventDefault();
				header.changePasswordForUser();
			}
		});
	},
	
	closeChangePassDialog: function() {
		$("#changePasswordConfirm").dialog("close");
	},
	
	changePasswordForUser: function() {

		var dataToSend = $("#changePassForm").serialize();

		var validationResult = header.validateChangePassData();
		
		if(validationResult) {
			$.ajax({
				type: "POST",
				url: "ChangeIAPassword",
				data: dataToSend,
				dataType: "json",
				beforeSend: function() {
					main.showOperationsLoading();
				},
				complete: function() {
					main.hideOperationsLoading();
				},
				success: function(response) {
					if(main.isSessionExpiredOrGenericError(response, null)) {
						return;
					}	
					
					if(!response.ok) {
						main.showWarning(response.statusMessage);
					} else {
						main.showSuccess(response.statusMessage);
						header.closeChangePassDialog();
						$(".userMenu a[href='Logout']").parent().click();
					}
				},
				error: function(jqXHR, status, error) {
					if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
						return;
					}
				}
			});
		} 
	},
	
	validateChangePassData: function() {
		var valid = false;
		
		var pass = $("#newPass").val();
		var confirmPass = $("#confirmNewPass").val();
		
		if(pass.trim().length >= 6 && pass.trim().length <= 9) {
			if(confirmPass.trim() == pass.trim()) {
				valid = true;
			} else {
				main.showWarning(auxTexts.invalidConfirmChangePassword);
			}
		} else {
			main.showWarning(auxTexts.invalidSizeChangePassword);
		}
		
		return valid;
	},
	
	redirectToPage: function(actionName) {
//		main.showActionFeedbackWhenNeeded();
		header.isRedirect = true;

		var li = null;
		var td = null;
		
		console.log("redirectToPage: ", actionName);
		
		if(actionName == null) {
			li = $(".globalNavMenu").first().find("li:first-of-type");
			td = $(".globalNavButton").first();
			//retry operation: external modules menu items may not have finished loading
			if(li.length == 0){
				setTimeout(header.redirectToPage, 20);
			}
		} else {
			li = header.menuLiMapping[actionName];
			td = header.menuTdMapping[actionName];
		}
		
		li.click();
		li.addClass("selectedMenuItem");
		
		$(".globalNav td").removeClass("globalNavActive");
		td.addClass("globalNavActive");
	}
	
	
};

//used to encode and decode url hash
var Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t="";var n,r,i,s,o,u,a;var f=0;e=Base64._utf8_encode(e);while(f<e.length){n=e.charCodeAt(f++);r=e.charCodeAt(f++);i=e.charCodeAt(f++);s=n>>2;o=(n&3)<<4|r>>4;u=(r&15)<<2|i>>6;a=i&63;if(isNaN(r)){u=a=64}else if(isNaN(i)){a=64}t=t+this._keyStr.charAt(s)+this._keyStr.charAt(o)+this._keyStr.charAt(u)+this._keyStr.charAt(a)}return t},decode:function(e){var t="";var n,r,i;var s,o,u,a;var f=0;e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");while(f<e.length){s=this._keyStr.indexOf(e.charAt(f++));o=this._keyStr.indexOf(e.charAt(f++));u=this._keyStr.indexOf(e.charAt(f++));a=this._keyStr.indexOf(e.charAt(f++));n=s<<2|o>>4;r=(o&15)<<4|u>>2;i=(u&3)<<6|a;t=t+String.fromCharCode(n);if(u!=64){t=t+String.fromCharCode(r)}if(a!=64){t=t+String.fromCharCode(i)}}t=Base64._utf8_decode(t);return t},_utf8_encode:function(e){e=e.replace(/\r\n/g,"\n");var t="";for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);if(r<128){t+=String.fromCharCode(r)}else if(r>127&&r<2048){t+=String.fromCharCode(r>>6|192);t+=String.fromCharCode(r&63|128)}else{t+=String.fromCharCode(r>>12|224);t+=String.fromCharCode(r>>6&63|128);t+=String.fromCharCode(r&63|128)}}return t},_utf8_decode:function(e){var t="";var n=0;var r=c1=c2=0;while(n<e.length){r=e.charCodeAt(n);if(r<128){t+=String.fromCharCode(r);n++}else if(r>191&&r<224){c2=e.charCodeAt(n+1);t+=String.fromCharCode((r&31)<<6|c2&63);n+=2}else{c2=e.charCodeAt(n+1);c3=e.charCodeAt(n+2);t+=String.fromCharCode((r&15)<<12|(c2&63)<<6|c3&63);n+=3}}return t}};
