var login = {
		
	isLoginForDownload: false,
	
	buildLoginDialog : function() {
		var loginButtons = {};
		loginButtons[auxTexts.buttonCleanText] = function() {
			$("#loginForm input").val("");
		};
		loginButtons[auxTexts.buttonConfirmText] = login.performAuthentication;

		$("#loginDialog").dialog({
			dialogClass : "dialogNoTitle",
			draggable : false,
			resizable : false,
			buttons : loginButtons
		});

		$("#loginDialog").css("visibility", "visible");

		main.hideLoading();

		main.showActionFeedbackWhenNeeded();

		var txtUsername = $("#txtUsername");
		var txtPassword = $("#txtPassword");
		
		main.focusLater(txtUsername);

		txtUsername.keypress(function(event) {
			if (event.which == 13 && txtUsername.val() != "") {
				event.preventDefault();
				main.focusLater(txtPassword);
			}
		});

		txtPassword.keypress(function(event) {
			if (event.which == 13 && txtPassword.val() != "") {
				event.preventDefault();
				login.performAuthentication();
			}
		});
	}, 
	
	buildLoginDialogIA : function() {
		var loginButtons = {};
		loginButtons[auxTexts.buttonCleanText] = function() {
			$("#loginForm input").val("");
		};
		loginButtons[auxTexts.buttonConfirmText] = login.sendRequest;

		$("#loginDialog").dialog({
			dialogClass : "dialogNoTitle",
			draggable : false,
			resizable : false,
			buttons : loginButtons
		});

		$("#loginDialog").css("visibility", "visible");

		main.hideLoading();

		main.showActionFeedbackWhenNeeded();

		var txtUsername = $("#txtUsername");
		var txtPassword = $("#txtPassword");
		var forgotPassword = $("#forgotPassword");
		
		main.focusLater(txtUsername);

		txtUsername.keypress(function(event) {
			if (event.which == 13 && txtUsername.val() != "") {
				event.preventDefault();
				main.focusLater(txtPassword);
			}
		});

		txtPassword.keypress(function(event) {
			if (event.which == 13 && txtPassword.val() != "") {
				event.preventDefault();
				if(forgotPassword.is(':checked')){
					login.performForgotPasswordRequest();
				} else {
					login.performAuthentication();
				}
			}
		});
		
		forgotPassword.click(function() {

			if(forgotPassword.is(':checked')){
				txtPassword.prop('disabled', true);
				txtPassword.prop('value', "");
				login.isLogin = false;
			} else{
				txtPassword.prop('disabled', false);
				login.isLogin = true;
			}
		});
		
	}, 
	
	performAuthentication: function() {
		$.ajax({
			type: "POST",
			cache: false,
			url: login.isLoginForDownload ? "LoginForDownload" : "Login",
			dataType: "json",
			data: $("#loginForm").serialize(),
			beforeSend: function() {
				main.showLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}								
				if (response.statusCode != "OK" && response.statusCode != "PASSWORD_SHOULD_BE_UPDATED" ) {
					main.showWarning(response.statusMessage);
					main.hideLoading();
				} else {
					if(login.isLoginForDownload){
						login.buildDownloadPage();
					}else{
						window.location = "SIRE";
					}
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	

	performForgotPasswordRequest: function() {
		$.ajax({
			type: "POST",
			cache: false,
			url: "ForgotPassword",
			dataType: "json",
			data: $("#txtUsername").serialize(),
			beforeSend: function() {
				main.showLoading();
			},
			success: function(response) {
				if (response.statusCode != "OK" ) {
					main.showWarning(response.statusMessage);
					main.hideLoading();
				} else {
					window.location = "RESETPASSWORD";
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	sendRequest: function() {
		var txtUsername = $("#txtUsername");
		var txtPassword = $("#txtPassword");

		if(!$("#forgotPassword").prop("checked")){
			if(txtUsername.val() == "" || txtPassword.val() == ""){
				main.showWarning(auxTexts.usernameAndPasswordMissing);
			} else {
				login.performAuthentication();
			}
		}else {
			if(txtUsername.val() == ""){
				main.showWarning(auxTexts.usernameMissing);
			} else {
				login.performForgotPasswordRequest()
			}
		}
	},
	
	resizeContent: function() {
		if($("#loginDialog").hasClass('ui-dialog-content')) {
			$("#loginDialog").dialog({position: {
				my : "center",
				at : "center",
				of : window
			}});
		}
		
		if($("#mManagerDialog").hasClass('ui-dialog-content') && !login.isLoginForDownload) {
			$("#mManagerDialog").dialog({position: {
				my : "center",
				at : "center",
				of : window
			}});
		}
	},
	
	openMManagerDialog: function() {
		main.minWidth = 100;
		header.buildGlobalNavMenus();
		
		var mManagerButtons = {};
		mManagerButtons[auxTexts.openMManagerApp] = function() {
			window.location="intent://#Intent;scheme=mmanager;end;";
			window.setTimeout(function(){
				$("#mManagerDialog").html(auxTexts.errorOpeningManager);
			}, 500);
		};
		mManagerButtons[auxTexts.downloadMManagerApp] = function() {
			login.isLoginForDownload = true;
			login.goToLogin();
		};
		mManagerButtons[auxTexts.ignoreMManagerApp] = function() {
			login.isLoginForDownload = false;
			login.goToLogin();
		};
		
		$("#mManagerDialog").dialog({
			dialogClass : "dialogNoTitle",
			draggable : true,
			resizable : false,
			buttons : mManagerButtons
		});
		$("#mManagerDialog").parent().css("width","390px");
		$("#mManagerDialog").css("visibility", "visible");

		main.hideLoading();
	},

	
	goToLogin: function() {
		
		$('div.ui-dialog[aria-describedby="mManagerDialog"]').hide();
		main.showLoading();
		login.buildLoginDialog();
		main.hideLoading();
	},
	
	buildDownloadPage: function(){
		$('div.ui-dialog[aria-describedby="loginDialog"]').hide();
		$("#downloadDiv").css("visibility", "visible");
		main.hideLoading();
		document.getElementById("apkLink").click();
	}
	
};

$(window).resize(login.resizeContent);