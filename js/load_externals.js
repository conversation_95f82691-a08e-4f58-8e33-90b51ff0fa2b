var loadExternals = {
	
	numberOfModules: 0,
		
	loadExternalModules: function() {
		
		loadExternals.checkForExternalModules();
		
	}, 
	
	checkForExternalModules: function() {
		
		$.ajax({
			type: "GET",
			url: "CheckForExternalModules",
			dataType: "json",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
									
				if(!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					
					if (response.value == null || response.value == "") {
						loadExternals.hideMenuButtonIfnoModules();
						return;
					} 
					 
					var modulesArray = response.value.split(",");
					
					var permissions = main.checkPermissions(modulesArray);
					loadExternals.numberOfModules = 0;
					
					//cycle through permissions since they contain all modules to be loaded
					for (module in permissions) {
						if (permissions[module] === true) {
							loadExternals.numberOfModules ++;
							loadExternals.getExternalModuleInfo(module);
						}
						
					}
					
					//if there are no permissions for any module, remove menu button
					if (loadExternals.numberOfModules === 0) {
						loadExternals.hideMenuButtonIfnoModules();
					}
					
				}
				
			},
			error: function(jqXHR, status, error) {
				//when in login page the call comes here
			}
		});
		
	},
	
	hideMenuButtonIfnoModules: function() {
		if (!$("#externalAppsMenu").has("li").length) {
			$("#externalAppsMenu").remove();
			header.resizeMenu();
		}
	},
		
	getExternalModuleInfo: function(moduleName) {
		
		$.ajax({
			type: "GET",
			url: moduleName + "ModuleInfo",
			dataType: "json",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
									
				if(!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					
					if (response.value == null || response.value == "") {
						return;
					} 
					
					//name of module must match action call for struts so we insert it here directly on the href
					var moduleData = response.value.split(",");
					var moduleEntry = $("<li><a href=" + moduleData[0] + ">"+ moduleData[1] + "</s:text></a></li>");
					$("#externalModulesMenuList").append(moduleEntry);
					main.hideLoading();
					
					header.addClickActionToMenuItem(moduleEntry, $("#externalAppsMenu"));					

					loadExternals.numberOfModules--;
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	}
	
};
