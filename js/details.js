var details = {
	dialogDetailsType: null,
	detailsComponent: null,
	optionsSelectComponent: null,
	
	entityIdName: null,
	entityIdValue: null,
	entityData: null,
	entityName: null,
	entityContextOptionsMapping: null,
	
	executeAfterFillDetailsScreen: null,
	optionSelectChange: null,
	
	subEntityContextOptionsMapping: null,
	subEntityData: null,
	subEntityName: null,
	
	entityIdentifier: null,
	
	contextMenuButton: null,
	
	detailsContentsRealHeight: null,
	
	successCallback: null,
	
	isImageToEdit: false,
	isFileToEdit: false,
	
	isBannerType: false,
	
	expirationDateComponent: null,
	
	buildInnerDetailsComponent: function(detailsComponentId) {
		details.dialogDetailsType = "innerDialogDetails";
		details.detailsComponent = $("#" + detailsComponentId);
		details.detailsComponent.hide();
		$.ajax({
			type: "GET",
			url: "SetCurrentEntityForDetails?entityIdentifier=" + details.entityIdentifier,
			success: function(response) {
				if(main.isSessionExpired(response)) {
					return;
				}
				
				details.detailsComponent.html(response);
				details.optionsSelectComponent = $("#detailsOptions");
				
				details.detailsComponent.dialog({
					dialogClass : "innerDialogDetails",
					draggable : false,
					resizable : false,
					width: 690,
					minHeight : 10,
					modal : false,
					autoOpen : false,
					position : { my: "left top", at: "left+5 top+5", of: $("#detailsRightContainer"), collision: "none" }
				});
				
				main.registerElementToDestroy(details.detailsComponent);
				main.registerDetailsDialogToDestroy(detailsComponentId);
				
				main.applyClearableStyles(details.detailsComponent);
				
				main.applySelectMenuStyles(details.detailsComponent);
				
				main.applyButtonsetStyles(details.detailsComponent);
				
				main.applyEntityButtonStyles(details.detailsComponent);
				
				details.getDetailsFromEntity();
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildDetailsComponent: function(detailsComponentId) {
		details.dialogDetailsType = "dialogDetails";
		details.detailsComponent = $("#" + detailsComponentId);
		details.detailsComponent.hide();
		$.ajax({
			type: "GET",
			url: "SetCurrentEntityForDetails?entityIdentifier=" + details.entityIdentifier,
			success: function(response) {
				if(main.isSessionExpired(response)) {
					return;
				}
				
				details.detailsComponent.html(response);
				details.optionsSelectComponent = $("#detailsOptions");
				
				details.detailsComponent.dialog({
					dialogClass : "dialogDetails",
					draggable : false,
					resizable : false,
					width: 690,
					minHeight : 10,
					modal : true,
					autoOpen : false,
					position : { my: "top", at: "top+100", of: window },
					open : function(event, ui) {
						$('.ui-widget-overlay').unbind("click").click(function() {
							details.closeDetailsDialog();
						});
						$('.ui-widget-overlay').addClass("withOpacity");
					},
					close : function(event, ui) {
						$('.ui-widget-overlay').removeClass("withOpacity");
						details.optionsSelectComponent.selectmenu("index", 0);
						main.destroyInnerComponents();
					}
				});
				
				main.registerElementToDestroy(details.detailsComponent);
				main.registerDetailsDialogToDestroy(detailsComponentId);
				
				main.applyClearableStyles(details.detailsComponent);
				
				main.applySelectMenuStyles(details.detailsComponent);
				
				main.applyButtonsetStyles(details.detailsComponent);
				
				main.applyEntityButtonStyles(details.detailsComponent);

				details.getDetailsFromEntity();
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	reloadDetailsDialog: function() {
		details.closeDetailsDialog();
		main.destroyDetailsDialogs();

		if (details.dialogDetailsType == "innerDialogDetails") {
			main.destroyInnerComponents();
			details.buildInnerDetailsComponent(details.detailsComponent.attr("id"));
			
		} else if (details.dialogDetailsType == "dialogDetails") {
			details.buildDetailsComponent(details.detailsComponent.attr("id"));
		}
	},
	
	getDetailsFromEntity: function(idValue) {
		$.ajax({
			type: "GET",
			cache: false,
			dataType: "json",
			url: details.action + "?" + details.entityIdName + "=" + details.entityIdValue + "&entityIdentifier=" + details.entityIdentifier,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				
				if (!response.reply.ok) {
					main.showWarning(response.reply.statusMessage);
				} else {
					details.fillDetailsScreen(response);
					details.openDetailsDialog();
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	getSubEntityDetails: function(entityIdentifier, entityIdValue) {
		$.ajax({
			type: "GET",
			cache: false,
			dataType: "json",
			url: "GetSubEntityDetails?entityIdValue=" + entityIdValue + "&entityIdentifier=" + entityIdentifier,
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				
				if (!response.reply.ok) {
					main.showWarning(response.reply.statusMessage);
				} else {
					details.subEntityData = response;
					details.subEntityName = entityIdentifier.split(".", 3)[1];
					details.fillSubEntityDetails();
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	getFieldValue: function(fieldName) {
		var fieldValue = null;

		var field = details.getAttributeByName(fieldName);
		
		if(field != null) {
			var originalFieldName = field.name;
			if(originalFieldName.indexOf(".") != -1) {
				fieldValue = details.entityData.reply[details.entityName];
				var parts = originalFieldName.split(".");
				for(var i=0 ; i< parts.length ; i++) {
					fieldValue = fieldValue[parts[i]];
				}
			} else {
				fieldValue = details.entityData.reply[details.entityName][originalFieldName];
			}
		} else {
			fieldValue = null;
		}
		
		return fieldValue;
	},
	
	isAttributeEnabledOnEdit: function(attrName) {
		return details.getAttributeByName(attrName).enabledOnEdit;
	},
	
	getAttributeEnabledOnEditConditions: function(attrName) {
		return details.getAttributeByName(attrName).editConditions;
	},
	
	getAttributeEnabledOnEditPermissions: function(attrName) {
		return details.getAttributeByName(attrName).editPermissions;
	},
	
	getAttributeByName: function(attrName) {
		var attributes = details.entityData.entity.attributes;
		
		for(var i=0; i<attributes.length; i++) {
			var attributeName = details.entityData.entity.attributes[i].id;
			if(attributeName == attrName) {
				return details.entityData.entity.attributes[i];
			}
		}
		
		return null;
	},
	
	fillDetailsScreen: function(entityData) {
		console.log("#### entity data ", entityData);
		details.entityData = entityData;
		
		$(".detailsContainer tr td:last-of-type div:empty").each(function() {
			var div = $(this);
			
			div.html("<i>" + auxTexts.unknowninfo + "</i>");
		});
		
		details.detailsComponent.find("#body_generaldata table tr td:last-of-type:not(#extraDetailsTd) .generalData").each(function(){
			var div = $(this);
			var fieldName = div.attr("id");
			var fieldValue = details.getFieldValue(fieldName);
			var button = div.next();
			
			div.attr("original", fieldValue);
			
			var transformFunctionName = div.attr("transform");
			if(transformFunctionName && transformFunctionName.length > 0) {
				var fn = transformFunctions[transformFunctionName];
				fieldValue = fn(fieldValue, "display");
			}
			
			// HAMMER TIME - CAMPAIGNS - ALLOW TO SET SOURCE ON IMAGE ELEMENTS
			if ($(this).next('.hackerImageLink').length === 1) {
				if (!fieldValue) {
					$(this).next('.hackerImageLink').remove();
					$(this).css("display", "block");
				} else {
					$(this).next('.hackerImageLink').attr('href', fieldValue);
					$(this).next('.hackerImageLink').find('img.generalData').attr('src', fieldValue);	
				}
				
			}			
			
			// HAMMER TIME - CAMPAIGNS - ALLOW TO SET data-hackerPdfLink value
			if ($(this).next('.hackerPdf').length === 1) {
				if (!fieldValue) {
					$(this).next('.hackerPdf').remove();
					$(this).css("display", "block");
				} else {
					$(this).next('.hackerPdf').attr('href', fieldValue);	
				}				
			}
			
			///////////// here ////////////////////////
			var campaignType = details.entityData.reply.campaignItem && details.entityData.reply.campaignItem.type;
			if (campaignType === 'MESSAGE_OF_THE_DAY' || campaignType === 'SUPPORT_MESSAGE') {
				campaignType = 'message'
			}
			if (details.entityData.reply.campaignItem && 
				(campaignType === 'BANNER' || campaignType === 'message') && 
				 !$(this).closest('tr').hasClass(campaignType.toLowerCase())) {
					$(this).closest('tr').remove();
			}

			
			if(fieldValue == null || fieldValue.length == 0) {
				fieldValue = "<i>" + auxTexts.unknowninfo + "</i>";
				if(button.is("button")) {
					button.hide();
				}
			} else {
				if(button.is("button")) {
					var entityIdentifier = button.attr("id");
					var entityIdValue = div.attr("original");
					var exceptionValue = details.getAttributeByName(fieldName).entityFieldExceptionValue;

					if(entityIdValue == exceptionValue) {
						button.hide();
					} else {
						button.show();
						button.unbind("click").click(function() {
							details.contextMenuButton = button;
							details.getSubEntityDetails(entityIdentifier, entityIdValue);
						});
					}
					
				}
			}
			
			div.html(fieldValue);
		});
		
		if(details.executeAfterFillDetailsScreen != null) {
			details.executeAfterFillDetailsScreen();
		}
		
		if(details.optionSelectChange != null) {
			details.optionSelectChange();
		} else {
			details.optionsSelectComponent.unbind("change");
			details.optionsSelectComponent.change(function(){
				var selectedOption = $(this).val();
				details.showNewOptionContent(selectedOption);
			});
		}
		
		var entityMenuOptions = details.entityData.entity.contextMenu;
		
		details.optionsSelectComponent.find("option[value^=cm_]").each(function(index) {
			$(this).remove();
		});
		
		details.entityContextOptionsMapping = new Array();
		
		for(var i=0; i<entityMenuOptions.length; i++) {
			details.optionsSelectComponent.append("<option value='cm_" + i + "'>" + entityMenuOptions[i].textId + "</option>");
			details.entityContextOptionsMapping.push(entityMenuOptions[i]);
		}
		
		details.optionsSelectComponent.selectmenu({width: 350});
		details.optionsSelectComponent.selectmenu("value", "generaldata");
		
		details.detailsComponent.dialog("open");
		
		details.fillEditDetailsScreen();
		
		details.addExtraDetails();
		details.showNewOptionContent("generaldata");

		$("#editop_productSubTypeId").unbind("onchange").change(function() {
            details.adjustExtensionDeviceDay($("option:selected", this).val());
        });

        $("#editop_productSubTypeId").trigger("change");

		details.detailsComponent.show();
	},
	adjustExtensionDeviceDay: function(productSubType) {

        var productSubTypesNuovoString = "" + productSubTypesNuovo;

        if(productSubTypesNuovoString){
             var productSubTypeArray = productSubTypesNuovoString.split(",");

             if(productSubTypeArray.indexOf("" + productSubType) > -1){
                $("#editop_unitel_device_extension_days").closest("tr").show();
             } else {
                $("#editop_unitel_device_extension_days").closest("tr").hide();
             }
        }
    },
	addExtraDetails: function() {
		
		// Remove any previous extraDetailsComponent
		$("#extraDetailsTd").closest( 'tr' ).remove();
		
		// Find out if this entity is blacklisted or deleted
		var deletedValue = details.getFieldValue("deleted");
		var blackListedValue = details.getFieldValue("blackListed");
		var activeValue = details.getFieldValue("enabledProduct");
		var hasPendingUpdates = details.getFieldValue("hasPendingUpdates");
		var isEditable = details.getFieldValue("editable");
		
		// If not there is nothing to do here
		if ((deletedValue != true) && (blackListedValue != true) && (activeValue != false)
				&& (hasPendingUpdates != true) && (isEditable != false)) {
			$("#entitiesTreeTable .selected td").css("color", "#6A6A6A");
			
			return;
		}
		
		// Add label to the top of the details form
		var extraDetailsHtml = '<tr><td colspan="2" id="extraDetailsTd"></td></tr>';
		var extraDetails = $(extraDetailsHtml);
		$("#body_generaldata table tr:first-of-type").before(extraDetails);
		
		// Set style as deleted
		if (deletedValue == true) {
			$("#extraDetailsTd").addClass("deletedWarning");
			$("#entitiesTreeTable .selected td").css("color", "#FF0800");
			
			if (details.entityName == "tec") {
				$("#extraDetailsTd").html(auxTexts.tecDeleted);
			} else {
				$("#extraDetailsTd").html(auxTexts.entityDeleted);
			}
			
			return;
		}

		// Set style as blacklisted
		if (blackListedValue == true) {
			$("#extraDetailsTd").addClass("blackListedWarning");
			$("#entitiesTreeTable .selected td").css("color", "#10A1D7");

			if (details.entityName == "tec") {
				$("#extraDetailsTd").html(auxTexts.tecBlacklisted);
			} else {
				$("#extraDetailsTd").html(auxTexts.entityBlacklisted);
			}
			
			return;
		}
		
		// Set details and selected row style
		if (activeValue == false) {
			$("#extraDetailsTd").addClass("inactivedWarning");
			$("#entitiesTreeTable .selected td").css("color", "#10A1D7");

			$("#extraDetailsTd").html(auxTexts.productInactive);
			
			return;
		}
		
		// Set details and selected row style
		if (hasPendingUpdates == true) {
			$("#extraDetailsTd").addClass("hasPendingUpdatesWarning");
			$("#entitiesTreeTable .selected td").css("color", "#10A1D7");

			$("#extraDetailsTd").html(auxTexts.hasPendingUpdates);
		}
		
		// Set details style
		if (isEditable == false) {
			$("#extraDetailsTd").addClass("inactivedWarning");

			$("#extraDetailsTd").html(auxTexts.ruleNotEditable);
			
			return;
		}
	},
	
	executeEntityContextMenuAction: function(selectedOption) {
		
		var optionData = details.entityContextOptionsMapping[selectedOption];
		if(optionData != null) {
			var menuHref = optionData.targetMenuHref;
			var filters = optionData.filters;
			for(var i=0 ; i<filters.length ; i++) {
				var filter = filters[i];
				var filterType = filter.type;
				var filterName = filter.filterName;
				var filterAttribute = filter.valueAttribute;
				var filterDirectValue = filter.directValue;
				
				if(filterType == "LIST") {
					var filterValue = filterDirectValue;
					if (filterAttribute != null && filterAttribute.length > 0) {
						filterValue = details.entityData.reply[details.entityName][filterAttribute];
					}
					
					if(filterName.indexOf(".") != -1) {
						var filterValuesReal = filterName.split(".");
						filterName = filterValuesReal[0];
						filterValue = filterValuesReal[1] + "=" + filterValue; 
					}
					
					search.extraSearchFilters = new Object();
					search.extraSearchFilters[filterName] = filterValue;

					if(search.useDateSearch) {
						if(search.dateSearchType == 'both') {
//							if(search.fromDateSearchComponent != null && search.toDateSearchComponent != null) {
								var startTimestamp = $.datepicker.formatDate('@', search.fromDateSearchComponent.datepicker("getDate"));
								search.extraSearchFilters['fromDate'] = startTimestamp;
								
								var endTimestamp = $.datepicker.formatDate('@', search.toDateSearchComponent.datepicker("getDate"));
								search.extraSearchFilters['toDate'] = endTimestamp;
//							}
							
						} else if(search.dateSearchType == 'from') {
//							if(search.fromDateSearchComponent != null) {
								var startTimestamp = $.datepicker.formatDate('@', search.fromDateSearchComponent.datepicker("getDate"));
								search.extraSearchFilters['fromDate'] = startTimestamp;
//							}
							
						} else if(search.dateSearchType == 'to') {
//							if(search.toDateSearchComponent) {
								var endTimestamp = $.datepicker.formatDate('@', search.toDateSearchComponent.datepicker("getDate"));
								search.extraSearchFilters['toDate'] = endTimestamp;
//							}
						}
					}
					
					if(search.useThreeStateFilters) {
						search.threeStateComponents.each(function() {
							var fieldId = $(this).attr("id");
							var checkedOption = $(this).find(":radio:checked");
							var value = checkedOption.val();
							
							var currentFilterValue = search.extraSearchFilters[fieldId];
							
							if(currentFilterValue != null && currentFilterValue.length > 0) {
								value = currentFilterValue;
							}
							search.extraSearchFilters[fieldId] = value;
						});
					}
					
					
					header.redirectToPage(menuHref);
				} else if(filterType == "TREE") {
					if (filterAttribute != null && filterAttribute.length > 0) {
						filterValue = details.entityData.reply[details.entityName][filterAttribute];
						main.treeSearchValue = filterValue;
					}
					
					header.redirectToPage(menuHref);
				}
			}
		}
	},
	
	
	fillSubEntityDetails: function() {
		var subEntityAttributes = details.subEntityData.entity.attributes;
		var subEntityValues = details.subEntityData.reply[details.subEntityName];
		var subEntityMenuOptions = details.subEntityData.entity.contextMenu;
		
		main.destroyDetailsDialog("entityContextDialog");
//		main.destroySelect("contextMenuOptions");
		
		$("#entityContextDialog").hide();
		
		var optionsSelect = $("#contextMenuOptions");
		optionsSelect.find("option:not(:first-of-type)").each(function(index) {
			$(this).remove();
		});
		optionsSelect.unbind("change");
		optionsSelect.change(details.executeContextMenuAction);
		
		details.subEntityContextOptionsMapping = new Array();
		details.subEntityContextOptionsMapping.push(null);
		
		for(var i=0; i<subEntityMenuOptions.length; i++) {
			optionsSelect.append("<option value='" + (i + 1) + "'>" + subEntityMenuOptions[i].textId + "</option>");
			details.subEntityContextOptionsMapping.push(subEntityMenuOptions[i]);
		}
		
		optionsSelect.selectmenu({width: 350});
		main.registerSelectToDestroy("contextMenuOptions");
		
		$("#entityContextDialog").attr("title", auxTexts.contextMenuTitle.replace("[entity]", eval("entitiesLabels." + details.subEntityName)));
		
		$("#entityContextDialog").dialog({
			dialogClass : "dialogContextMenu",
			draggable : false,
			resizable : false,
			width: 400,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { 
				my: "right top", 
				at: "left-2 top", 
				of: details.contextMenuButton
			},
			open : function(event, ui) {
				$('.ui-widget-overlay').click(function() {
					$("#entityContextDialog").dialog("close");
				});
			},
			close : function(event, ui) {
			}
		});
		main.registerDetailsDialogToDestroy("entityContextDialog");
		
		$("#contextMenuItems table").html("");
		for(var j=0 ; j<subEntityAttributes.length ; j++) {
			
			var attr = subEntityAttributes[j];
			var fieldValue = subEntityValues[attr.name];
			
			if(attr.name.indexOf(".") != -1) {
				fieldValue = subEntityValues;
				var parts = attr.name.split(".");
				for(var k=0 ; k< parts.length ; k++) {
					fieldValue = fieldValue[parts[k]];
				}
			}
			
			var transformFunctionName = attr.transformFunction;
			if(transformFunctionName != null && transformFunctionName.length > 0) {
				var fn = transformFunctions[transformFunctionName];
				fieldValue = fn(fieldValue, "display");
			}
			
			$("#contextMenuItems table").append("<tr><td><div>" + attr.textId + "</div></td><td><div>" + fieldValue + "</div></td></tr>");
		}
		
		$("#entityContextDialog").dialog("open");
	},
	
	executeContextMenuAction: function() {
		var selectedOption = this.value;
		
		var optionData = details.subEntityContextOptionsMapping[selectedOption];
		if(optionData != null) {
			var menuHref = optionData.targetMenuHref;
			var filters = optionData.filters;
			for(var i=0 ; i<filters.length ; i++) {
				var filter = filters[i];
				var filterType = filter.type;
				var filterName = filter.filterName;
				var filterAttribute = filter.valueAttribute;
				var filterDirectValue = filter.directValue;
				
				if(filterType == "LIST") {
					var filterValue = filterDirectValue;
					if (filterAttribute != null && filterAttribute.length > 0) {
						filterValue = details.subEntityData.reply[details.subEntityName][filterAttribute];
					}
					
					if(filterName.indexOf(".") != -1) {
						var filterValuesReal = filterName.split(".");
						filterName = filterValuesReal[0];
						filterValue = filterValuesReal[1] + "=" + filterValue; 
					}
					
					search.extraSearchFilters = new Object();
					search.extraSearchFilters[filterName] = filterValue;
					
					header.redirectToPage(menuHref);
				} else if(filterType == "TREE") {
					if (filterAttribute != null && filterAttribute.length > 0) {
						filterValue = details.subEntityData.reply[details.subEntityName][filterAttribute];
						main.treeSearchValue = filterValue;
					}
					
					header.redirectToPage(menuHref);
				}
			}
		}
	},
	
	fillEditDetailsScreen: function() {
		
		$("#body_editdata table tr td:last-of-type").each(function(){
			var td = $(this);
			var fieldNameElement = td.find("#entityFieldName");
			var fieldValueElement = fieldNameElement.next();
			
			fieldValueElement.val("");
			
			var dataElement = fieldNameElement.parent().children(":not(input[type='hidden'])").first();

			var attrName = fieldNameElement.val();
			var fieldValue = details.getFieldValue(attrName);
			var fieldEnabledOnEdit = details.isAttributeEnabledOnEdit(attrName);
			var fieldEnabledOnEditConditions = details.getAttributeEnabledOnEditConditions(attrName);
			var fieldEnabledOnEditPermissions = details.getAttributeEnabledOnEditPermissions(attrName);
			
			if(fieldEnabledOnEdit) {
				if(Object.keys(fieldEnabledOnEditConditions).length > 0) {
					var keys = Object.keys(fieldEnabledOnEditConditions);
					for(var i=0; i<keys.length; i++) {
						var key = keys[i];
						var value = fieldEnabledOnEditConditions[key];
						
						var parts = value.split(",");
						var validCondition = false;
						for(var j=0; j<parts.length; j++) {
							if(details.getFieldValue(key) == parts[j]) {
								validCondition = true;
								break;
							}
						}
						fieldEnabledOnEdit = fieldEnabledOnEdit && validCondition;
					}
				} else if(fieldEnabledOnEditPermissions != null && fieldEnabledOnEditPermissions.length > 0) {
					var permissions = fieldEnabledOnEditPermissions.split(",");
					
					for(var j=0 ; j<permissions.length ; j++) {
						var hasPermission = main.checkPermission(permissions[j]);
						
						fieldEnabledOnEdit = fieldEnabledOnEdit && hasPermission;
					};
					
				}
			}
			
			// change the value
			fieldValueElement.val(fieldValue);
			
			// add the value to the input/select
			if(dataElement.is("select")) {
					
				dataElement.selectmenu("value", fieldValue);
				
				if(("" + dataElement.selectmenu("value")) != ("" + fieldValue)) {
					dataElement.selectmenu("index", 0);
				}
				
				if(!fieldEnabledOnEdit) {
					dataElement.selectmenu("option", "disabled", true);
				} else {
					dataElement.selectmenu("option", "disabled", false);
				}
				
			} else if (dataElement.hasClass("clearable")){
				var input = dataElement.find("input");
				input.val("");
				input.blur();
				
				if(fieldValue != null) {
					var transformFunctionName = td.attr("transform");
					if(transformFunctionName.length > 0) {
						var fn = transformFunctions[transformFunctionName];
						fieldValue = fn(fieldValue, "display");
					}
					
					input.val(fieldValue);
					// remove the watermark so it has the same appearence of the normal user input
					input.removeClass("watermark");
				}
				
				if(!fieldEnabledOnEdit) {
					input.attr("disabled", "disabled");
					dataElement.find("a").hide();
					input.css("opacity", "0.8");
				} else {
					input.removeAttr("disabled");
					dataElement.find("a").show();
					input.css("opacity", "1");
				}
				
				td.removeClass("invalidValue");
				input.parent().removeClass("invalidValue");
				input.removeClass("invalidValue");
				// HACK
				if (dataElement.hasClass('isImage')) {
					// console.log("### GHOST ELEMENT ", dataElement);
					var ghostElementValue = $(dataElement).find('input').val();
					
					$(dataElement).next('.js-clonedSrc').find('img').attr('src', ghostElementValue);
					
					if (ghostElementValue.length > 0) {
						$('input.editImage').parent().css('display', 'none');
					} else {
						$(dataElement).next('.js-clonedSrc').css('display','none');
					}
				}
				if (dataElement.hasClass('isPdfFile')) {
					//console.log('### is pdf');
					var pdfPathValue = $(dataElement).find('input').val();
					if (pdfPathValue.length === 0) {
						$(dataElement).next('.isPdfFile').css('display', 'none');
						$(dataElement).parent().find('.editFile').css('display', 'block');
					} else {	
						$(dataElement).parent().find('.editFile').css('display', 'none');
					}
					
				}
				
				/////// here /////////////////
				var campaignType = details.entityData.reply.campaignItem && details.entityData.reply.campaignItem.type;
				if (campaignType === 'MESSAGE_OF_THE_DAY' || campaignType === 'SUPPORT_MESSAGE') {
					campaignType = 'message'
				}
				if (details.entityData.reply.campaignItem && 
					(campaignType === 'BANNER' || campaignType === 'message') && 
					 !$(this).closest('tr').hasClass(campaignType.toLowerCase())) {
						$(this).closest('tr').remove();
				}
				
				
				/*if (dataElement.hasClass('clearableElement')) {
					debugger;
					dataElement.find('a.clearlink').addClass('customClearElement').unbind('click').bind('click');
					dataElement.next('div').css("display", "none")
				}*/
				
			} else if(dataElement.is("div")) {
				dataElement.find("input[type='radio']").removeAttr("checked");
				var input = dataElement.find("input[value='" + fieldValue + "']");
				input.click();
				
				if(!fieldEnabledOnEdit) {
					dataElement.buttonset("option", "disabled", true);
				} else {
					dataElement.buttonset("option", "disabled", false);
				}
			} 
		});
		
		//HACK PARA NA EDICAO DE CAMPANHA ELIMINAR IMAGEM E APRESENTAR NOVO CAMPO DE UPLOAD 
		$('.customClearElement').click(function() {
			console.log("#### clique to upload new file or image");
			if ($(this).hasClass('isImage')) {
				console.log("#### LIMPA a imagem");
				details.isImageToEdit = true;
				
			} else if ($(this).hasClass('isPdfFile')) {
				console.log("#### LIMPA o pdf");
				details.isFileToEdit = true;
			}
			
			
			$(this).next().css('display', 'block');
			$(this).next().find('input').css('display', 'inline-block');
			$(this).remove();
			details.detailsContentsRealHeight = $(".detailsBodyContent:visible table.detailsContainer").height();
			details.resizeInnerContents();
		});
		
		$('.handleClearNewFile').find('a.clearlink').click(function() {
			// console.log("### clique para limpar o novo ficheiro / imagem");
			
			if ($(this).parent().hasClass('editImage')) {
				// console.log("#### LIMPA a imagem");
				campaignsTree.newCampaignImage = null;
				
			} else if ($(this).parent().hasClass('editFile')) {
				// console.log("#### LIMPA o pdf");
				campaignsTree.newCampaignPdfFile = null;
			}
		});
		
		var expirationDateValue = $('#expirationDate').text();
		
		details.expirationDateComponent = $("#expirationDateEdit");
		
		var currentDate = new Date();
		
		details.expirationDateComponent.datetimepicker({
			onClose : function(selectedDate) {
				// console.log("### CLOSE date picker");
				campaignsTree.adjustCalandarStyles();
			},
		 	dateFormat: "dd-mm-yy",
			showButtonPanel: true,
			timeOnlyShowDate: false,
			showHour: false,
			showMinute: false
		});
		
		var dateRegex = /\d{2}\-\d{2}\-\d{4}/g;
		// Avalia se o expirationDateValue faz match com o formato dd-mm-yyyy
		// senao fizer entao define o setDate a vazio pk senao é apresentada a data de hoje :| 
		if(dateRegex.test(expirationDateValue)) {
			details.expirationDateComponent.datepicker('setDate', expirationDateValue);
		} else {
			details.expirationDateComponent.datepicker('setDate', '');
		}
		details.expirationDateComponent.datepicker('option', "minDate", currentDate);
		
		$('#expirationDateEdit').click(function() {
			// console.log("#### clique no input da expiration date");
			$(this).siblings('.ui-datepicker-trigger').trigger('click');
		});
	},
	// remove o valor da hora no campo expiration date.
	handleUpdateExpirationDate: function(el) {
		$(el).val($(el).val().split(' ')[0]);		
	},
	
	validateDataToSave: function() {
		var validationResult = true;
		$('#entityEditForm tr:visible #entityFieldName').each(function() {
			var nameInput = $(this);
			var td = nameInput.parent();
			var input = td.find("input:not(input[type='hidden'])").first();
			
			var key = nameInput.val();
			var value = input.val();
			
			var attribute = details.getAttributeByName(key);
			var isValid = details.validateDataValue(attribute, value, td);

			if(!isValid) {
				main.showWarning(auxTexts.errorValidatePropertyText);
				
				td.addClass("invalidValue");
				input.parent().addClass("invalidValue");
				input.addClass("invalidValue");
				
				if(attribute.validationTextId != null) {
					input.unbind("click").click(function() {
						main.hideWarning();
						main.showWarning(attribute.validationTextId);
					});
				}
				
				validationResult = false;
			} else {
				td.removeClass("invalidValue");
				input.parent().removeClass("invalidValue");
				input.removeClass("invalidValue");
				
				if(attribute.validationTextId != null) {
					input.unbind("click");
				}
			}
		});
		return validationResult;
	},
	
	_parseStringDate: function(date) {
		var dateArr = date.split('-');
		return dateArr[1] + "/" +dateArr[0]+ "/" +dateArr[2];
	},
	
	_isValidDate: function (s) {
	    // Assumes s is "mm/dd/yyyy"
	    if ( ! /^\d\d\/\d\d\/\d\d\d\d$/.test(s) ) {
	        return false;
	    }
		const parts = s.split('/').map((p) => parseInt(p, 10));
	    parts[0] -= 1;
	    var d = new Date(parts[2], parts[0], parts[1]);
	    return d.getMonth() === parts[0] && d.getDate() === parts[1] && d.getFullYear() === parts[2];
	},
	
	validateDataValue: function(attribute, value, td) {
		var regex = attribute.regex;
		
		if(attribute.type == 'UNITS') {
			var unformatedValue = main.getUnformatedValue(value);
			var convertedValue = main.convertExternalUnitsToInternalUnits(unformatedValue);
			td.find("#unitsTransformInput").val(convertedValue);
			
			return main.validateInput(convertedValue, regex);
			
		} else if(attribute.type == 'CURRENCY') {
			var unformatedValue = main.getUnformatedValue(value);
			var convertedValue = main.convertExternalCurrencyToInternalCurrency(unformatedValue);
			td.find("#currencyTransformInput").val(convertedValue);
			
			return main.validateInput(convertedValue, regex);
			
		} else if (attribute.type == 'CAMPAIGNDATE') {
			if (value) {
				console.log("### é campaign date ", value);
				var parsedDate = details._parseStringDate(value); 
				return details._isValidDate(parsedDate);
			}			
		} else {
			if(regex != null) {
				return main.validateInput(value, regex);
			}
		}
		
		return true;
	},
	
	confirmSave: function() {
		var editForm = $('#entityEditForm');
		var validationResult = details.validateDataToSave();
		// debugger;
		if(validationResult) {
			
			var idInput = editForm.find('tr:first-of-type input').removeAttr('disabled');
			var dataToSend = editForm.serialize();
			idInput.attr('disabled','disabled');
			
			var url = "EditEntityDetails?entityIdentifier=" + details.entityIdentifier;
			var isProcessData = true;
			var contentTypeValue = 'application/x-www-form-urlencoded; charset=UTF-8';
			var campaignId = null;

			
			//HAMMER TIME - DEVERIA ESTAR NUMA CONSTANTE ESTE VALOR
			if (details.entityIdentifier === 'campaign') {
				
				url = "EditCampaign";
				isProcessData = false;
				contentTypeValue = false;
				campaignId = editForm[0]["entityData['id']"].value;
				
				var formData = new FormData();
				
				dataToSend = formData;
				formData.append('campaignId', campaignId);
				formData.append('title', editForm[0]["entityData['title']"] ? editForm[0]["entityData['title']"].value : '');
				formData.append('campaignEnabled', editForm[0]["entityData['isEnabled']"].value);				
				formData.append('description', editForm[0]["entityData['description']"] ? editForm[0]["entityData['description']"].value : '');
			 	formData.append('campaignHighlighted', editForm[0]["entityData['isHighlighted']"].value);
				formData.append('expirationDate', editForm[0]["entityData['expirationDate']"].value);	
				
				/////////////// no changes - submit file & image as received//////////
				// URL image
				if (editForm[0]["entityData['image']"] && editForm[0]["entityData['image']"].value.length > 0 && !details.isImageToEdit) {
					formData.append	('campaignImageName',  editForm[0]["entityData['image']"].value.split("/").pop())
				}
				
				if (campaignsTree.newCampaignImage){
					formData.append('campaignImage', campaignsTree.newCampaignImage);
					formData.append('campaignImageName', campaignsTree.newCampaignImage.name);	
				}
				
				// URL file
				if (editForm[0]["entityData['file']"] && editForm[0]["entityData['file']"].value.length > 0 && !details.isFileToEdit) {
					formData.append	('campaignFileName',  editForm[0]["entityData['file']"].value.split("/").pop())
				}
				if (campaignsTree.newCampaignPdfFile){
					formData.append('campaignFile', campaignsTree.newCampaignPdfFile);
					formData.append('campaignFileName', campaignsTree.newCampaignPdfFile.name)	
				}
				
			}

			$.ajax({
				type: "POST",
				url: url,
				data: dataToSend,
				dataType: "json",
				processData: isProcessData,
				contentType: contentTypeValue,
				enctype:"multipart/form-data",
				beforeSend: function() {
					main.showOperationsLoading();
				},
				complete: function() {
					
					if (details.entityIdentifier === 'campaign') {
						// Reset newCampaignImage & newCampaignPdfFile properties
						campaignsTree.newCampaignImage = null;
						campaignsTree.newCampaignPdfFile = null;
						// Force to load details properly - fix image and pdf issue
						campaignsTree.showDetailsForCampaigns(campaignId);	
						details.isImageToEdit = false;
						details.isFileToEdit = false;					
					}
				},
				success: function(response) {
					if(main.isSessionExpiredOrGenericError(response, null)) {
						return;
					}
										
					if(!response.ok) {
						main.showWarning(response.statusMessage);
					} else {
						search.applyConfigurations();
						main.showSuccess(response.statusMessage);
						details.getDetailsFromEntity(details.entityIdValue);
						
						// Execute success callback
						if(details.successCallback != null && typeof details.successCallback === 'function') {
							details.successCallback();
						}
					}
				},
				error: function(jqXHR, status, error) {
					main.showWarning(auxTexts.editError);
					if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
						return;
					}
				}
			});
		}
		
	},
	
	closeDetailsDialog: function() {
		if(details.detailsComponent != null && details.detailsComponent.dialog("isOpen")) {
			details.detailsComponent.dialog("close");
		}
	},
	
	openDetailsDialog: function() {
		if(details.detailsComponent != null && !details.detailsComponent.dialog("isOpen")) {
			details.detailsComponent.dialog("open");
		}
	},
	
	showNewOptionContent: function(selectedOption) {
		$(".invalidValue").removeClass("invalidValue");
		if (selectedOption == "editdata") {
			
			var allButtons = [
             {text: auxTexts.buttonCancelText, click: details.closeDetailsDialog},
             {text: auxTexts.buttonApplyText, click: details.confirmSave}
             ];
			details.showEditButtons(allButtons);
		} else {
			details.detailsComponent.dialog("option", "buttons", []);
			
			if(selectedOption.indexOf("cm_") !== -1) {
				var index = parseInt(selectedOption.replace("cm_", ""), 10);
				details.executeEntityContextMenuAction(index);
			}
		}
		
		details.detailsComponent.find(".detailsBodyContent").hide();
		$("#body_" + selectedOption).show();

		details.detailsContentsRealHeight = $(".detailsBodyContent:visible table.detailsContainer").height();
		details.resizeInnerContents();
	},
	
	showEditButtons: function(allButtons) {
		
		if(details.dialogDetailsType == "dialogDetails") {
			details.detailsComponent.dialog("option", "buttons", allButtons);
		} else if(details.dialogDetailsType == "innerDialogDetails") {
			var allButtonsExceptCancel = new Array();
			for(var i=0; i<allButtons.length; i++) {
				var button = allButtons[i];
				if(button.text != auxTexts.buttonCancelText) {
					allButtonsExceptCancel.push(button);
				}
			}
			details.detailsComponent.dialog("option", "buttons", allButtonsExceptCancel);
		}
	},
	
	resizeInnerContents: function() {
		if(details.detailsContentsRealHeight != null) {
			if(main.maxDetailsDialogHeight - 60 < details.detailsContentsRealHeight) {
				$(".detailsBodyContent").height(main.maxDetailsDialogHeight - 60);
			} else {
				$(".detailsBodyContent").height(details.detailsContentsRealHeight);
			}
			
			$(".detailsBodyContent").css("overflow", "auto");
		}
		else {
			if($("#saleStatsTableCharts:visible").length > 0) {
				
				if(main.maxDetailsDialogHeight - 60 > $("#saleStatsTableCharts").height()) {
					$(".detailsBodyContent").height($("#saleStatsTableCharts").height());
					$(".detailsBodyContent").css("overflow", "hidden");
				} else {
					$(".detailsBodyContent").height(main.maxDetailsDialogHeight - 60);
					$(".detailsBodyContent").css("overflow", "auto");
				}
				
			} else if ($("#body_transactionsSummary:visible").length > 0) {
				$(".detailsBodyContent").height(220);
				$(".detailsBodyContent").css("overflow", "hidden");
				
			} else {
				$(".detailsBodyContent").css("overflow", "hidden");
				$(".detailsBodyContent").height(main.maxDetailsDialogHeight - 60);
			}
		}
	},
	
	resizeContents: function() {
		details.resizeInnerContents();

		if (details.detailsComponent != null && details.detailsComponent.hasClass('ui-dialog-content')
				&& details.detailsComponent.dialog("isOpen")) {
			
			if (details.detailsComponent.dialog("option", "dialogClass") == "dialogDetails") {
				details.detailsComponent.dialog({
					position : {
						my : "top",
						at : "top+100",
						of : window
					}
				});

			} else if (details.detailsComponent.dialog("option", "dialogClass") == "innerDialogDetails") {
				details.detailsComponent.dialog({
					position : {
						my : "left top",
						at : "left+5 top+5",
						of : $("#detailsRightContainer"),
						collision : "none"
					}
				});
			}
		}
		
		if($("#entityContextDialog").hasClass('ui-dialog-content')) {
			$("#entityContextDialog").dialog({position : { 
				my: "right top", 
				at: "left-2 top", 
				of: details.contextMenuButton
			}});
		}
	}
};

$(window).resize(details.resizeContents);