var transformFunctions = {

	highlightInProductionOrAvailableTecAppVersion: function(aData, nRow, tooltipText) {
		if(aData["inProduction"] == false) {
			$('td', nRow).css("color", "#FF0800");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.notInProduction;
		} else if (aData["availableForUpdate"] == false) {
			$('td', nRow).css("color", "#10A1D7");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.notAvailableForUpdate;
		}
		return tooltipText;
	},
	
	highlightOutadetedTecs: function(aData, nRow, tooltipText) {
		if(aData["tecVersion"].inProduction == false) {
			$('td', nRow).css("color", "#FF0800");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.mandatoryUpdate;
		}
		return tooltipText;
	},

	highlightBlackListedOrDeletedEntities: function(aData, nRow, tooltipText) {
		if (aData["deleted"] == true) {
			$('td', nRow).css("color", "#FF0800");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.entityDeleted;
		} else if (aData["blackListed"] == true) {
			$('td', nRow).css("color", "#10A1D7");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.entityBlacklisted;
		} 
		return tooltipText;
	},
	
	highlightBlackListedOrDeletedTecs: function(aData, nRow, tooltipText) {
		if (aData["deleted"] == true) {
			$('td', nRow).css("color", "#FF0800");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.tecDeleted;
		} else if (aData["blackListed"] == true) {
			$('td', nRow).css("color", "#10A1D7");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.tecBlacklisted;
		}
		return tooltipText;
	},
	
	highlightAlarmsState: function(aData, nRow, tooltipText) {
		if (aData["open"] == true) {
			$('td', nRow).css("color", "#FF0800");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.alarmOpen;
		} else if (aData["open"] == false) {
			$('td', nRow).css("color", "#10A1D7");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.alarmClosed;
		}
		return tooltipText;
	},
	
	highlightReledbackTransactions: function(aData, nRow, tooltipText) {
		if (aData["rolledback"] == true) {
			$('td', nRow).css("color", "#FF0800");
			tooltipText += auxTexts.transactionRolledback;
		} 
		return tooltipText;
	},
	
	highlightResolvedEmisTransactions: function(aData, nRow, tooltipText) {
		if (aData["markedAsResolved"] == true) {
			$('td', nRow).css("color", "#10A1D7");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.transactionResolved;
		}
		return tooltipText;
	},
	
	highlightDeletedStock: function(aData, nRow, tooltipText) {
		if (aData["removed"] == true) {
			$('td', nRow).css("color", "#FF0800");
			tooltipText += auxTexts.transactionRolledback;
		} 
		return tooltipText;
	},
	
	highlightNotEditableRules: function(aData, nRow, tooltipText) {
		if (aData["editable"] == false) {
			$('td', nRow).css("color", "#10A1D7");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.ruleNotEditable;
		}
		return tooltipText;
	},
	
	highlightViewedOrDeletedNotifications: function(aData, nRow, tooltipText) {
		if (aData["deleted"] != null) {
			$('td', nRow).css("color", "#FF0800");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.notificationDeleted;
		} else if (aData["viewed"] == true) {
			$('td', nRow).css("color", "#10A1D7");
			if(tooltipText.length > 0) {
				tooltipText += "\n";
			}
			tooltipText += auxTexts.notificationViewed;
		} 
		return tooltipText;
	},
	
	transformTecStateById : function(id) {
		return eval("tecStatesI18N.s" + id);
	},
	
	transformActiveInactive: function(state) {
		if(state) {
			return auxTexts.active;
		} else {
			return auxTexts.inactive;
		}
	},
	
	transformVersionId: function(versionId) {
		return $("#tecVersionsEnum" + versionId).val();
	},
	
	transformYesNo: function(state) {
		if(state) {
			return auxTexts.yes;
		} else {
			return auxTexts.no;
		}
	},
	
	parseDate: function(date) {
		if (date) {
			return new Date(date).toLocaleDateString().replaceAll('/', '-');
		}
	},
	
	transformProductSubTypeId: function(subTypeId) {

		if(subTypeId == null) {
			return "";
		}

		return $("#productSubTypesEnum" + subTypeId).val();
	},

	transformProductType : function(id) {
		if(id == null) {
			return "";
		}
		return eval("productTypesDescriptionI18N.s" + id);
	},
	
	transformProductTypeByName : function(source) {
		if(source == null) {
			return "";
		}
		
		return eval("productTypesDescriptionByNameI18N." + source.toLowerCase());
	},
	// CAMPAIGNS
	transformCampaignType : function(id) {
		if(id == null) {
			return "";
		}
		return eval("campaignTypesDescriptionI18N.s" + id);
	},
	/*
	transformBase64ToImage: function(base64String) {
		if(base64String && base64String.length > 0) {
			return 'data:image/gif;base64,'+base64String;
		}
		return null;
	},
	*/
	
	transformBase64ToPdfFile: function(base64String) {
		// debugger
		if(base64String && base64String.length > 0) {
			return 'data:application/pdf;base64,'+base64String;
		}
		return null;
	},
	
	transformTransactionTotalTypeText : function(source) {
		if(source == null) {
			return "";
		}
		
		return eval("transactionTotalTypesI18N." + source.toLowerCase());
	},
	
	transformTransactionType : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("transactionTypesI18N." + source.toLowerCase());
	},
	
	transformMobileMoneyTransactionState : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("transactionStateI18N." + source.toLowerCase());
	},
	
	transformMobileMoneyTransactionType : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("mobileMoneyTransactionTypesI18N." + source.toLowerCase());
	},
	
	transformMobileMoneyState : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("mobileMoneyStatesI18N." + source.toLowerCase());
	},
	
	transformEntityType : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("entityTypesI18N." + source.toLowerCase());
	},
	
	transformTecAppType : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("tecAppTypesI18N." + source.toLowerCase());
	},
	
	transformEntityTypeText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("entityTypesI18N." + source.toLowerCase());
	},
	
	transformServiceLevelValueInText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("serviceLevelsI18N." + source);
	},
	
	transformTecTypeText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("tecTypesByNameI18N." + source.toLowerCase());
	},	
	
	transformTecStateText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("tecStatesByNameI18N." + source.toLowerCase());
	},

	transformTecUpdateStateText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("tecUpdateStatesByNameI18N." + source.toLowerCase());
	},

	transformTecUpdateTypeText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("tecUpdateTypeByNameI18N." + source.toLowerCase());
	},
	
	transformAlarmSeverityValueInText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		var severityName = eval("alarmSeveritiesI18N." + source.toLowerCase());
		
		if(type==="sort") {
			return severityName;
		} else if(type==="display") {
			return "<span class='severity_" + source.toLowerCase()  + "'>" + severityName + "</span>";
		} 
		
		return severityName;
	},
	
	transformAlarmTypeValueInText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("alarmTypesI18N." + source.toLowerCase());
	},
	
	transformEventTypeValueInText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		var formatedValue;
		
		if(source == "BLACKLIST") {
			var direction = val.finalState;
			if(direction == "I") {
				formatedValue = timelineLabels.blacklistIn;
			} else {
				formatedValue = timelineLabels.blacklistOut;
			}
			
		} else {
			formatedValue = eval("eventTypesI18N." + source.toLowerCase());
		}
		
		return formatedValue;
	},
	
	transformTimelineUser : function(source, type, val) {
		if (val.eventType == "STATE_CHANGE" || val.eventType == "BLACKLIST") {
			
			if (val.username) {
				return source;
			} else {
				return timelineLabels.system;
			}
			
		} else {
			return " ";
		}
	},
	
	transformTimelineDataValue : function(source, type, val) {
		
		var formattedValue = "";
		
		if(val.eventType == 'STATE_CHANGE') {
			formattedValue = timelineLabels.fromTo;
			
			if(val.initialState != null) {
				formattedValue = formattedValue.replace("[0]", eval("tecStatesByNameI18N." + val.initialState.toLowerCase()));
			} else {
				formattedValue = formattedValue.replace("[0]", tecStatesByNameI18N.unknown);
			}

			if(val.finalState != null) {
				formattedValue = formattedValue.replace("[1]", eval("tecStatesByNameI18N." + val.finalState.toLowerCase()));

				if(val.finalState.toLowerCase() == "associated" || val.finalState.toLowerCase() == "associating") {
					var storeInfo = " " + timelineLabels.toStore;
					storeInfo = storeInfo.replace("[0]", val.storeId);
					
					formattedValue += storeInfo;
				} 
				if(source.length > 0) {
					if(formattedValue.length > 0) {
						formattedValue += ": ";
					}
					formattedValue += source;
				}
			}
			
		} else if(val.eventType == 'BLACKLIST') {
			formattedValue = source;
		} else if(val.eventType == 'VERSION_UPDATE') {
			formattedValue = timelineLabels.fromTo;
			
			formattedValue = formattedValue.replace("[0]", val.initialState);
			
			formattedValue = formattedValue.replace("[1]", val.finalState);
		} 
		
		return formattedValue;
	},
	
	transformUnitsValue: function(source, type, val) {
		if(source == null) {
			return "";
		}

		if(type==="sort") {
			return parseInt(source, 10);
		} else if(type==="display") {
			return main.getAmountFormatedValue(source, true);
		}
		
		return source;
	},
	
	transformPlafondValue: function(source, type, val) {
		if(source == null) {
			return auxTexts.unlimited;
		}
		
		if(type==="sort") {
			return parseInt(source, 10);
		} else if(type==="display") {
			return main.getAmountFormatedValue(source, true);
		}
		
		return source;
	},
	
	transformCurrencyValue: function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		if(type==="sort") {
			return parseInt(source, 10);
		} else if(type==="display") {
			return main.getCurrencyFormatedValue(source, true);
		}
		
		return source;
	},
	
//	transformNewCurrencyValue: function(source, type, val) {
//		if(source == null) {
//			return "";
//		}
//		
//		if(type==="sort") {
//			return parseInt(source, 10);
//		} else if(type==="display") {
//			source =  main.convertInternalUnitsToExternalUnits(source);
//			return main.getCurrencyFormatedValue(source, true);
//		}
//		
//		return source;
//	},
	
	transformProductUnitsValue: function(source) {
		if(source == null) {
			return "";
		}
		
		return main.getAmountFormatedValue(source, false);
	},
	
	transformProductCurrencyValue: function(source) {
		if(source == null) {
			return "";
		}

		return main.getCurrencyFormatedValue(source, false);
	},

	transformPercentValue: function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		if(type==="sort") {
			return source;
		} else if(type==="display") {
			return main.getPercentFormatedValue(source);
		}
		
		return source;
	},
	
	transformDateTimeValue: function(source, type, val) {
		if(source == null) {
			return "";
		}
		if(type==="sort") {
			return source;
		} else if(type==="display") {
			return main.transformTimestampValueInDateTimeText(source, formats.dateSimple);
		}
		
		return source;
	},
	
	transformDateTimeCompleteValue: function(source, type, val) {
		if(source == null) {
			return "";
		}
		if(type==="sort") {
			return source;
		} else if(type==="display") {
			return main.transformTimestampValueInDateTimeText(source, formats.dateComplete);
		}
		
		return source;
	},
	
	transformTimeValue: function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		if(type==="sort") {
			return source;
		} else if(type==="display") {
			return main.transformTimestampValueInDateTimeText(source, formats.hourFull);
		}
		
		return source;
	},
	
	transformMsValue: function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return main.getMsFormatedValue(source);
	},
	
	transformParamsDataValue: function(source, type, val) {
		if(source == null) {
			return "";
		}
		var encText = $('<div/>').text(source).html();
		return encText;
	},
	
	transformDaysValue: function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return main.getDaysFormatedValue(source);
	},
	
	transformCategoryValueInText: function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("categoriesI18N." + source.toLowerCase());
	},
	
	transformAccessTypeValueInText: function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("readWriteI18N." + source.toLowerCase());
	},
	
	transformServiceValueInText: function(source, type, val) {
		if(source == null) {
			return "";
		}
		return eval("servicesI18N." + source.toLowerCase());
	},
	
	transformOpTypeAggregatorValueInText: function(source, type, val) {
		if(source == null) {
			return "";
		}
		return eval("opTypeAggregatorsI18N." + source.toLowerCase());
	},
	
	transformResultAggregatorValueInText: function(source, type, val) {
		if(source == null) {
			return "";
		}
		return eval("resultAggregatorsI18N." + source.toLowerCase());
	},
	
	transformChannelValueInText: function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("channelsI18N." + source.toLowerCase());
	},
	
	transformOperationValueInText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("operationI18N.op" + source.toLowerCase());
	},
	
	transformResultTypeValueInText: function(source, type, val) {
		if (source == null) {
			return "";
		}
		
		return eval("resultTypeI18N." + source.toLowerCase());
	},
	
	transformSmsText: function(source, type, val) {
		return source;
	},
	
	transformSubAgentValueForParentId: function(source, type, val) {
		var agentId = val.topHierarchyEntity;
		var subAgentId = val.parentId;
		
		if(subAgentId == null || agentId == null || agentId == subAgentId) {
			return "";
		} 
		
		return subAgentId;
	},
	
	transformSubAgentValueForAgentId: function(source, type, val) {
		var agentId = val.topHierarchyEntity;
		var subAgentId = val.agentId;
		
		if(subAgentId == null || agentId == null || agentId == subAgentId) {
			return "";
		} 
		
		return subAgentId;
	},
	
	transformSubAgentValueForTecAgentId: function(source, type, val) {
		var agentId = val.topHierarchyEntity;
		var subAgentId = val.tecAgentId;
		
		if(subAgentId == null || agentId == null || agentId == subAgentId) {
			return "";
		} 
		
		return subAgentId;
	},
	
	transformSupervisorProfiles: function(profileId) {
		if (profileId == null) {
			return "";
		}
		return $("#supervisorProfilesEnum" + profileId).val();
	},
	
	transformBoUserProfiles: function(profileId) {
		if (profileId == null) {
			return "";
		}
		return $("#boUserProfilesEnum" + profileId).val();
	},
	
	transformTecDevices: function(deviceId) {
		if (deviceId == null) {
			return "";
		}
		
		return $("#tecDevicesEnum" + deviceId).val();
	},
	
	transformDeviceType : function(id) {
		if(id == null) {
			return "";
		}
		
		return eval("deviceTypesI18N.d" + id);
	},
	
	transformDeviceTypeName : function(name) {
		if(name == null) {
			return "";
		}
		
		return eval("deviceTypesI18N." + name.toLowerCase());
	},
	
	transformProductConfiguration: function(source, type, val) {
		if (source == null) {
			return "";
		}
		
		return eval("productConfigurationI18N." + source.toLowerCase());
	},
	
	transformProfileType: function(source, type, val) {
		if (source == null) {
			return "";
		}
		
		return eval("profileTypesDescriptionI18N." + source.toLowerCase());
	},

	transformProvisionStatusCode : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("provisionStatusCodesI18N." + source.toLowerCase());
	},
	
	transformPinStatusCode : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("pinStatusCodesI18N." + source.toLowerCase());
	},
	
	transformNullToNotAvailable : function(source, type, val) {
		if(source == null) {
			return "N/A";
		}
		
		return source;
	},
	
	transformSimSetupState : function(source) {
		if(source == null) {
			return "";
		}
		
		return eval("simSetupStatesByNameI18N." + source.toLowerCase());
	},
	
	transformPrtStatus : function(source) {
		if(source == null) {
			return "";
		}
		
		return eval("emisTransactionStatusI18N." + source.toLowerCase());
	},
	
	transformEmisConsolidationState : function(source) {
		if(source == null) {
			return "";
		}
		
		return eval("emisConsolidationStatesI18N." + source.toLowerCase());
	},
	
	transformApiValue : function(source) {
		if(source == null) {
			return "";
		}
		
		return eval("apiI18N." + source.toLowerCase());
	},
	
	transformNotificationTypeValueInText : function(source, type, val) {
		if(source == null) {
			return "";
		}
		
		return eval("notificationTypesI18N." + source.toLowerCase());
	},
	
	transformDeleted: function(source){
		if(source==null){
			return auxTexts.no;
		}
		return source;
	}
	
};
