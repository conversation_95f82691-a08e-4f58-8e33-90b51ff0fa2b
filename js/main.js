var main = {
		
	bodyHeight: 0,

	minWidth: 1230,
	
	maxDetailsDialogHeight: 0,
	
	maxConfigDialogHeight: 0,
	
	language: null,
	
	systemMonitoringSelectedMachine: 0,
	
	detailsDialogsToDestroy: new Array(),
	
	selectsToDestroy: new Array(),
	
	datepickersToDestroy: new Array(),
	
	elementsToDestroy: new Array(),
	
	timersToStop: new Array(),
	
	intervalsToStop: new Array(),
	
	innerComponentsToDestroy: new Array(),
	
	configDialogsToDestroy: new Array(),
	
	chartsLibsAlreadyLoaded: false,
	
	mapsLibsAlreadyLoaded: false,
	
	innerListsToDestroy: new Array(),
	
	treeSearchValue: null,
	
	warnTimer: null,
	
//	testAntennas: null,
	
	transformTimestampValueInDateTimeText: function(timestamp, format) {
		if(timestamp == null || timestamp.length == 0) {
			return;
		}
		if(typeof timestamp === 'string') {
			timestamp = parseInt(timestamp, 10);
		}
			
		return moment(new Date(timestamp)).format(format);
	},
	
	showLocationOnMap: function(coordinates, address) {
		window.open('https://maps.google.com/maps?q=' + coordinates + '+(' + address +')&z=14&ll=' + coordinates, '_blank');
	},

	getAmountFormatedValue : function(value, showUnits) {
		if(showUnits == null || showUnits == false) {
			accounting.settings.currency.symbol = "";
		} else {
			accounting.settings.currency.symbol = " " + auxTexts.creditText;
		}
		accounting.settings.currency.format = "" + formats.amountFormat;
		accounting.settings.currency.decimal = "" + formats.amountDecimal;
		accounting.settings.currency.thousand = "" + (formats.amountThousand === "space" ? " " : formats.amountThousand);
		accounting.settings.currency.precision = "" + formats.amountPrecision;

		var decValue = main.convertInternalUnitsToExternalUnits(value);
		
		return accounting.formatMoney(decValue);
	},
	
	getCurrencyFormatedValue : function(value, showUnits) {
		if(showUnits == null || showUnits == false) {
			accounting.settings.currency.symbol = "";
		} else {
			accounting.settings.currency.symbol = " " + auxTexts.currencyText;
		}
		accounting.settings.currency.format = "" + formats.currencyFormat;
		accounting.settings.currency.decimal = "" + formats.currencyDecimal;
		accounting.settings.currency.thousand = "" + (formats.currencyThousand === "space" ? " " : formats.currencyThousand);
		accounting.settings.currency.precision = "" + formats.currencyPrecision;
		
		var decValue = main.convertInternalCurrencyToExternalCurrency(value);
		
		return accounting.formatMoney(decValue);
	},
	
	getDecimalFormatedValue : function(value) {
		accounting.settings.currency.decimal = "" + formats.currencyDecimal;
		accounting.settings.currency.thousand = "" + (formats.currencyThousand === "space" ? " " : formats.currencyThousand);
		accounting.settings.currency.precision = "" + formats.currencyPrecision;

		return accounting.formatNumber(value);
	},
	
	getUnformatedValue : function(formatedValue) {
		accounting.settings.currency.decimal = "" + formats.currencyDecimal;
		accounting.settings.currency.thousand = "" + (formats.currencyThousand === "space" ? " " : formats.currencyThousand);
		accounting.settings.currency.precision = "" + formats.currencyPrecision;
		
		return accounting.unformat(formatedValue);
	},
	
	convertInternalUnitsToExternalUnits: function(value) {
		return value / formats.amountMultiplier;
	},
	
	convertExternalUnitsToInternalUnits: function(value) {
		return value * formats.amountMultiplier;
	},
	
	convertInternalCurrencyToExternalCurrency: function(value) {
		return value / formats.currencyMultiplier;
	},
	
	convertExternalCurrencyToInternalCurrency: function(value) {
		return value * formats.currencyMultiplier;
	},
	
	getPercentFormatedValue: function (value) {
		var unitsValue = Math.round(value * 100, 0);
		
		return unitsValue + "%";
	},
	
	getMsFormatedValue: function(value) {
		return value + " ms";
	},
	
	getDaysFormatedValue: function(value) {
		return value + " " + auxTexts.daysText;
	},
	
	transformBytesToSize: function (bytes) {
	   var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
	   if (bytes == 0) return '0 Bytes';
	   var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
	   return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
	},

	focusLater: function(elementToFocus) {
		setTimeout(function() {
			elementToFocus.focus();
		}, 100);
	},
	
	addWhiteSpacesInSmsText: function(source, type, val) {
		return source.replace(/;/g, " ; ").replace(/#/g," # ");
	},
	
	blurLater: function(elementToFocus) {
		setTimeout(function() {
			elementToFocus.blur();
		},410);
	},
	
	validateInput: function(value, regex) {
		var regExpression = new RegExp(regex);
		var valid = regExpression.test(value);
		return valid;
	},
	
	initialLoading: function() {
		main.resizeContent();
	},
	
	redirectExpired: function() {
		window.location = "Logout";
	},
	
	isSessionExpiredOrGenericError: function(response, jqXHR) {
		
		if(jqXHR != null) {
			if(jqXHR.responseText != null) {
				if(jqXHR.responseText.indexOf(auxTexts.sessionExpired) !== -1) {
					main.redirectExpired();
				}
			} else if (jqXHR.length > 0) {
				if(jqXHR.indexOf(auxTexts.sessionExpired) !== -1) {
					main.redirectExpired();
				}
			} else {
				main.showWarning(auxTexts.errorGeneric);
			}
			
			return true;
		}
		
		if(response != null){
			if(response.statusCode != null) {
				if(response.statusCode == "AUTHENTICATED_SESSION_EXPIRED") {
					main.redirectExpired();
					return true;
				}
			} else if(response.reply.statusCode != null) {
				if(response.reply.statusCode == "AUTHENTICATED_SESSION_EXPIRED") {
					main.redirectExpired();
					return true;
				}
			}
		}
		
		return false;
	},
	
	isSessionExpired: function(responseHtml) {
		if(responseHtml != null) {
			if(responseHtml.indexOf(auxTexts.sessionExpired) !== -1) {
				main.redirectExpired();
				return true;
			} 
		}

		return false;
	},
	
	showActionFeedbackWhenNeeded: function() {
		var errorText = $("#actionErrorText");
		var warningText = $("#actionWarningText");
		var successText = $("#actionSuccessText");

		if(errorText.val() != null) {
			$("#warningFeedback #warningText").html(errorText.val());
			$("#warningFeedback").show().delay(3000).fadeOut(500);
		} else if(warningText.val() != null) {
			$("#warningFeedback #warningText").html(warningText.val());
			$("#warningFeedback").show().delay(3000).fadeOut(500);
		}
		
		if(successText.val() != null) {
			$("#successFeedback #successText").html(successText.val());
			$("#successFeedback").show().delay(3000).fadeOut(500);
		}
	},
	
	showWarning: function(text) {
		$("#warningFeedback #warningText").html(text);
		
		$("#warningFeedback").show();
		
		main.warnTimer = setTimeout(function() {
			$("#warningFeedback").fadeOut(500);
		}, 3000)
	},
	
	hideWarning: function() {
		$("#warningFeedback").hide();
		clearTimeout(main.warnTimer);
	},
	
	showSuccess: function(text) {
		$("#successFeedback #successText").html(text);
		
		$("#successFeedback").show().delay(3000).fadeOut(500);
	},
	
	showLoading: function() {
		$("#loadingScreen").show();
	},
	
	hideLoading: function() {
		$("#loadingScreen").hide();
	},
	
	showTableLoading: function() {
		$("#tableLoading").show();
	},
	
	hideTableLoading: function() {
		$("#tableLoading").hide();
	},
	
	showInnerTableLoading: function() {
		$("#innerTableLoading").show();
	},
	
	hideInnerTableLoading: function() {
		$("#innerTableLoading").hide();
	},
	
	showOperationsLoading: function() {
		$("#operationsLoading").show();
	},
	
	hideOperationsLoading: function() {
		$("#operationsLoading").hide();
	},
	
	getContainerHeight : function() {
		return main.bodyHeight - 30;
	},
	
	resizeContent: function() {
		$("#body").height($(window).height() - 130);
		
		main.bodyHeight = $("#body").height();
		
		main.maxDetailsDialogHeight = main.bodyHeight - 50;
		main.maxConfigDialogHeight = main.bodyHeight - 20;
	},
	
	buildSingleContainer: function() {
		$(".singleContainer").accordion({
			icons : null
		});
		
		$(".singleContainer .ui-accordion-content").height(main.getContainerHeight());
	},
	
	resizeSingleContainer: function() {
		$(".singleContainer .ui-accordion-content").height(main.getContainerHeight());
	},
	
	buildDoubleContainer: function(rightWidthPercentage) {
		$(".doubleLeftContainer").accordion({
			icons : null
		});
		
		$(".doubleRightContainer").accordion({
			icons : null
		});
		
		$(".doubleLeftContainer .ui-accordion-content").height(main.getContainerHeight());
		$(".doubleRightContainer .ui-accordion-content").height(main.getContainerHeight());
		
		var width = $(window).width();
		
		if(width < main.minWidth) {
			width = main.minWidth;
		}
		
		var rightWidth = Math.round((width * rightWidthPercentage) - 20);
		var leftWidth = Math.round((width * (1 - rightWidthPercentage)) - 20);
		
		$(".doubleLeftContainer").width(leftWidth);
		$(".doubleRightContainer ").width(rightWidth);
	},
	
	resizeDoubleContainer: function(rightWidthPercentage) {
		$(".doubleLeftContainer .ui-accordion-content").height(main.getContainerHeight());
		$(".doubleRightContainer .ui-accordion-content").height(main.getContainerHeight());
		
		var width = $(window).width();
		
		if(width < main.minWidth) {
			width = main.minWidth;
		}
		
		var rightWidth = Math.round((width * rightWidthPercentage) - 20);
		var leftWidth = Math.round((width * (1 - rightWidthPercentage)) - 20);
		
		$(".doubleLeftContainer").width(leftWidth);
		$(".doubleRightContainer ").width(rightWidth);
	},
	
	buildDoubleFullContainer: function(rightWidthPercentage, isFullRightContainer) {
		$(".doubleLeftContainer").accordion({
			icons : null
		});
		
		$(".doubleRightContainer").accordion({
			icons : null
		});
		
		$(".doubleLeftContainer .ui-accordion-content").height(main.getContainerHeight());
		$(".doubleRightContainer .ui-accordion-content").height(main.getContainerHeight());
		
		var width = $(window).width();
		
		if(width < main.minWidth) {
			width = main.minWidth;
		}
		
		var rightWidth = Math.round((width * rightWidthPercentage) - 20);
		
		if(isFullRightContainer) {
			rightWidth = width - 20;
		}
		
		var leftWidth = Math.round((width * (1 - rightWidthPercentage)) - 20);
		
		$(".doubleLeftContainer").width(leftWidth);
		$(".doubleRightContainer ").width(rightWidth);
	},
	
	resizeDoubleFullContainer: function(rightWidthPercentage) {
		$(".doubleLeftContainer .ui-accordion-content").height(main.getContainerHeight());
		$(".doubleRightContainer .ui-accordion-content").height(main.getContainerHeight());
		
		var width = $(window).width();
		
		if(width < main.minWidth) {
			width = main.minWidth;
		}
		
		var rightWidth = Math.round((width * rightWidthPercentage) - 20);
		var leftWidth = Math.round((width * (1 - rightWidthPercentage)) - 20);
		
		$(".doubleLeftContainer").width(leftWidth);
		$(".doubleRightContainer ").width(rightWidth);
	},
	
	applyClearableStyles: function(containerComponent) {
		containerComponent.find('.clearable').each(function() {
			var input = $(this);
			
			input.clearable();

			input.blur(main.applyWatermark);
			input.focus(main.removeWatermark);
		});
		
	},
	
	applySelectMenuStyles: function(containerComponent) {
		containerComponent.find('.detailsContainer td select').each(function() {
			var select = $(this);
			var selectId = select.attr("id");
			select.selectmenu({width: 398});
			main.registerSelectToDestroy(selectId);
		});
		
	},
	
	applyButtonsetStyles: function(containerComponent) {
		containerComponent.find("div input[type='radio']:first-of-type").each(function() {
			var bset = $(this).parent();
			bset.buttonset();
		});
	},
	
	applyEntityButtonStyles: function(containerComponent) {
		containerComponent.find("button.targetEntity").each(function() {
			var button = $(this);
			var div = button.prev();
			
			button.button({
				text : false,
				icons : {
					primary : "ui-icon-info"
				}
			});
			div.width(div.parent().width() - 48);
		});
	},
	
	
	applyWatermark: function() {
		if($(this).hasClass("nowatermark")) {
			return;
		}
		if ( $(this).val() == '' ) { 
			$(this).change();
			$(this).val(this.defaultValue); 
			$(this).addClass("watermark");
		}
	},
	
	removeWatermark: function() {
		if($(this).hasClass("nowatermark")) {
			return;
		}
		
		if ( $(this).val() == this.defaultValue || $(this).val() == '' ) { 
			$(this).val(''); 
			$(this).removeClass("watermark");
		} 
		
		$(this).removeClass("invalidFilter");
	},
	
	getAuditDetails: function(clickedOriginalRowData, clickedFormattedRowData, index) {
		auditTrail.fillAuditDetails(clickedOriginalRowData);
	},
	
	getOperationHistoryDetails: function(clickedOriginalRowData, clickedFormattedRowData, index) {
		operationsHistory.fillOperationsHistoryDetails(clickedOriginalRowData);
	},
	
	getTecDetails: function(clickedOriginalRowData, clickedFormattedRowData, index) {
		tecs.getTecDetails(clickedOriginalRowData.tecId);
	},
	
	getTecRequestDetails: function(clickedOriginalRowData, clickedFormattedRowData, index) {
		tecCommStatsDays.getTecRequestDetails(clickedOriginalRowData.activityId, index);
	},
	
	zoomToSite : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		maps.zoomToSite(clickedOriginalRowData.code);
	},

	zoomToHeatmapSite : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		maps.zoomToSite(clickedOriginalRowData.site);
	},
	
	zoomToTecSite : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		maps.zoomToTecSite(clickedOriginalRowData.tecId);
	},

	zoomToSellerSite : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		maps.zoomToSellerSite(clickedOriginalRowData.id);
	},
	
	associateTecToStore : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		storesList.associateTecToStore(clickedOriginalRowData.tecId, clickedOriginalRowData.device.deviceType);
	},
	
	confirmSetInstallPinStore : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		agentsList.confirmSetInstallPinStore(clickedOriginalRowData.entityId);
	},
	
	transferSellerToStore : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		sellersList.transferSellerToStore(clickedOriginalRowData.entityId);
	},
	
	transferTecToEntity : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		tecs.transferTecToEntity(clickedOriginalRowData.entityId);
	},
	
	getProfileDetails: function(clickedOriginalRowData, clickedFormattedRowData, index) {
		profiles.getProfileDetails(clickedOriginalRowData.profileId);
	},
	
	getBOUserDetails: function(clickedOriginalRowData, clickedFormattedRowData, index) {
		boUsers.getBOUserDetails(clickedOriginalRowData.boUsername);
	},
	
	updateProductCatalogForEntities: function(clickedOriginalRowData, clickedFormattedRowData, index) {
		if (details.entityName == "agent") {
			agentsList.updateProductCatalogForEntities(clickedOriginalRowData);
		} else {
			storesList.updateProductCatalogForEntities(clickedOriginalRowData);
		}
	},
	
	updateTecAppVersionsForEntities: function(clickedOriginalRowData, clickedFormattedRowData, index) {
		if (details.entityName == "agent") {
			agentsList.updateTecAppVersionsForEntities(clickedOriginalRowData);
		} else {
			storesList.updateTecAppVersionsForEntities(clickedOriginalRowData);
		}
	},
	
	redirectToTecAuditTrail: function(tecId) {
		search.extraSearchFilters = new Object();
		search.extraSearchFilters["parametersData"] = "tecId=" + tecId;
		header.redirectToPage("PlatformAuditTrail");
	},
	
	redirectToTecListFilteredBySerial: function(tecSerial, tecType) {
		if(tecType === "INSTALLER_TEC") {
			var action = "InstallerTecs";
			search.extraSearchFilters = new Object();
			search.extraSearchFilters["serial"] = tecSerial;
			header.redirectToPage(action);
			
		} else {
			var action = "EntitiesTree";
			if (tecSerial != null && tecSerial.length > 0) {
				main.treeSearchValue = tecSerial;
			}
			
			header.redirectToPage(action);
		}
	},
	
	redirectToSellersListFilteredById: function(sellerId) {
		search.extraSearchFilters = new Object();
		search.extraSearchFilters["entityId"] = sellerId;
		header.redirectToPage("SellersList");
	},
	
	getTerminalBatchProvisionDetails : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		terminalBatchProvision.getTerminalBatchProvisionDetails(clickedOriginalRowData.batchNumber);
	},
	
	getRepositoryPinBatchProvisionDetails : function(clickedOriginalRowData, clickedFormattedRowData, index) {
		repositoryPinBatchProvision.getRepositoryPinBatchProvisionDetails(clickedOriginalRowData.batchNumber);
	},
	
/**
 * DIALOG AUX FUNCTIONS
 */
	registerElementToDestroy: function(jQueryElement) {
		main.elementsToDestroy.push(jQueryElement);
	}, 
	
	destroyRegisteredElements: function() {
		while(main.elementsToDestroy.length > 0) {
			var element = main.elementsToDestroy.pop();
			element.remove();
		}
		
		elementsToDestroy = new Array();
	},
	
	registerDetailsDialogToDestroy: function(dialogId) {
		var alreadyRegistered = false;
		for(var i=0 ; i<main.detailsDialogsToDestroy.length ; i++) {
			var id = main.detailsDialogsToDestroy[i];
			if(dialogId == id) {
				alreadyRegistered = true;
			}
		}
		if(!alreadyRegistered) {
			main.detailsDialogsToDestroy.push(dialogId);
		}
	},
	
	destroyDetailsDialogs: function() {
		while(main.detailsDialogsToDestroy.length > 0) {
			var dialogId = main.detailsDialogsToDestroy.pop();
			$("#" + dialogId).dialog("destroy");
			
			$("#" + dialogId + " button").each(function() {
				var button = $(this);
				button.unbind("click");
			});
		}
		
		main.detailsDialogsToDestroy = new Array();
	},
	
	destroyDetailsDialog: function(dialogId) {
		for(var i=0 ; i<main.detailsDialogsToDestroy.length ; i++) {
			var id = main.detailsDialogsToDestroy[i];
			if(dialogId == id) {
				$("#" + dialogId).dialog("destroy");
				main.detailsDialogsToDestroy.splice(i, 1);
			}
		}	
	},
	
	registerConfigDialogToDestroy: function(dialogId) {
		var alreadyRegistered = false;
		for(var i=0 ; i<main.configDialogsToDestroy.length ; i++) {
			var id = main.configDialogsToDestroy[i];
			if(dialogId == id) {
				alreadyRegistered = true;
			}
		}
		if(!alreadyRegistered) {
			main.configDialogsToDestroy.push(dialogId);
		}
	},
	
	destroyConfigsDialogs: function() {
		while(main.configDialogsToDestroy.length > 0) {
			var dialogId = main.configDialogsToDestroy.pop();
			$("#" + dialogId).dialog("destroy");
//			$("#" + dialogId).remove();
		}
		
		main.configDialogsToDestroy = new Array();
	},
	
	
	destroyConfigDialog: function(dialogId) {
		for(var i=0 ; i<main.configDialogsToDestroy.length ; i++) {
			var id = main.configDialogsToDestroy[i];
			if(dialogId == id) {
				$("#" + dialogId).dialog("destroy");
				main.configDialogsToDestroy.splice(i, 1);
			}
		}	
	},
	
	registerSelectToDestroy: function(selectId) {
		var alreadyRegistered = false;
		for(var i=0 ; i<main.selectsToDestroy.length ; i++) {
			var id = main.selectsToDestroy[i];
			if(selectId == id) {
				alreadyRegistered = true;
			}
		}
		if(!alreadyRegistered) {
			main.selectsToDestroy.push(selectId);
		}
	},
	
	destroySelects: function() {
		while(main.selectsToDestroy.length > 0) {
			var selectId = main.selectsToDestroy.pop();
			$("#" + selectId).selectmenu("destroy");
		}	
		main.selectsToDestroy = new Array();
	},
	
	destroySelect: function(selectId) {
		for(var i=0 ; i<main.selectsToDestroy.length ; i++) {
			var id = main.selectsToDestroy[i];
			if(selectId == id) {
				$("#" + selectId).selectmenu("destroy");
				main.selectsToDestroy.splice(i, 1);
			}
		}	
	},
	
	registerDatepickerToDestroy: function(datepickerId) {
		main.datepickersToDestroy.push(datepickerId);
	},
	
	destroyDatepickers: function() {
		while(main.datepickersToDestroy.length > 0) {
			var datepickerId = main.datepickersToDestroy.pop();
			$("#" + datepickerId).datepicker("destroy");
		}	
		main.datepickersToDestroy = new Array();
	},
	
	registerTimerToStop: function(timerId) {
		main.timersToStop.push(timerId);
	},
	
	stopTimers: function() {
		while(main.timersToStop.length > 0) {
			var timerId = main.timersToStop.pop();
			clearTimeout(timerId);
		}	
		
		main.timersToStop = new Array();
	},
	
	stopTimer: function(timerId) {
		for(var i=0 ; i<main.timersToStop.length ; i++) {
			var id = main.timersToStop[i];
			if(timerId == id) {
				clearTimeout(timerId);
				main.timersToStop.splice(i, 1);
			}
		}	
	},
	
	registerIntervalToStop: function(timerId) {
		main.intervalsToStop.push(timerId);
	},
	
	stopIntervals: function() {
		while(main.intervalsToStop.length > 0) {
			var timerId = main.intervalsToStop.pop();
			clearInterval(timerId);
		}	
		
		main.intervalsToStop = new Array();
	},
	
	stopInterval: function(timerId) {
		for(var i=0 ; i<main.intervalsToStop.length ; i++) {
			var id = main.intervalsToStop[i];
			if(timerId == id) {
				clearInterval(timerId);
				main.intervalsToStop.splice(i, 1);
			}
		}	
	},
	
	registerInnerComponentToDestroy: function(componentId) {
		main.innerComponentsToDestroy.push(componentId);
	}, 
	
	destroyInnerComponents: function() {
		var componentId = "";
		while(main.innerComponentsToDestroy.length > 0) {
			componentId = main.innerComponentsToDestroy.pop();
			main.destroySelect(componentId);
			main.destroyDetailsDialog(componentId);
			main.destroyConfigDialog(componentId);
		}

		$("#" + componentId).remove();
		
		main.innerComponentsToDestroy = new Array();
	},
	
	registerInnerListToDestroy: function(componentId) {
		var alreadyRegistered = false;
		for(var i=0 ; i<main.innerListsToDestroy.length ; i++) {
			var id = main.innerListsToDestroy[i];
			if(componentId == id) {
				alreadyRegistered = true;
			}
		}
		if(!alreadyRegistered) {
			main.innerListsToDestroy.push(componentId);
		}
	}, 
	
	destroyInnerListComponents: function() {
		var componentId = "";
		while(main.innerListsToDestroy.length > 0) {
			componentId = main.innerListsToDestroy.pop();
			$("#" + componentId).empty();
		}
		
		main.innerListsToDestroy = new Array();
	},
	
	loadGoogleJSApiLib: function(callbackFunction) {
		var scriptUrl = 'https://www.google.com/jsapi';
		
		$.getScript(scriptUrl, function() {
			main.googleJSApiLibAlreadyLoaded = true;
			
			callbackFunction();
		});
	},
	
	loadMapsLibs: function() {
		if(!main.googleJSApiLibAlreadyLoaded) {
			main.loadGoogleJSApiLib(main.loadMapsLibs);
		} else {

			var scriptUrl = 'https://maps.googleapis.com/maps/api/js?v=3&sensor=false&libraries=visualization&' +
			'key='+ mapApiKey + "&" + 
			'callback=main.loadExtraMapsLibs';
			
			$.getScript(scriptUrl);
		}
	},
	
	loadExtraMapsLibs: function() {
		var scriptUrl = 'js/oms.min.js';
		
		$.getScript(scriptUrl, function() {
			main.mapsLibsAlreadyLoaded = true;
			main.mapsCallbackFunction();
		});
	},
	
	loadChartsLibs: function(callbackFunction) {
		if(!main.googleJSApiLibAlreadyLoaded) {
			var callback = function() {
				main.loadChartsLibs(callbackFunction);
			};
			main.loadGoogleJSApiLib(callback);
		} else {
			var callback = function() {
				callbackFunction();
				main.chartsLibsAlreadyLoaded = true;
			};
			google.load('visualization', '1.0', {'packages':['corechart'], 'callback': callback});
		}
	},
	
	checkPermission: function(permissionName) {
		var hasPermission = false;
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			data: "permissionName" + "=" + permissionName,
			url: "CheckPermission?",
			async: false,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				if (response.ok && response.value == true) {
					hasPermission = true;
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
		
		return hasPermission;
	},
	
	checkPermissions: function(permissionNameArray) {
		var permissionsReply = [];

		var dataToSend = "";
		for (var i = 0; i < permissionNameArray.length; i++) {
			dataToSend += "permissionNameArray[" + i + "]=" + permissionNameArray[i];
			
			if (i < permissionNameArray.length - 1) {
				dataToSend += "&";
			}
		}
		
		$.ajax({
			type: "POST",
			cache: false,
			data: dataToSend,
			dataType: "json",
			url: "CheckPermissions?",
			async: false,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				
				if (response.ok) {
					permissionsReply = response.value;
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
		
		return permissionsReply;
	},
	
	getFilteredServices: function(selectedValue, targetSelectId, isConfigsScreen) {
		
		$.ajax({
			type: "GET",
			cache: false,
			dataType: "json",
			url: "FilteredServices?api=" + selectedValue,
			async: false,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				
				if (response.ok) {
					var servicesMap = response.value;
					
					var targetSelect = $("#" + targetSelectId);
					targetSelect.html("");
					
					var servicesKeys = Object.keys(servicesMap);
					var optionsHtml = "";
					
					for(var i=0 ; i< servicesKeys.length ; i++) {
						var key = servicesKeys[i];
						var value = servicesMap[key];
						optionsHtml += "<option value='" + key + "'>" + value + "</option>";
					}
					targetSelect.html(optionsHtml);

					$("#" + targetSelectId).change();
					
					main.destroySelect(targetSelectId);
					
					if(isConfigsScreen) {
						$("#" + targetSelectId).selectmenu({width: 278});
					} else {
						$("#" + targetSelectId).selectmenu({width: 398});
					}

					main.registerSelectToDestroy(targetSelectId);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	getFilteredOperations: function(selectedValue, targetSelectId, isConfigsScreen) {
		
		$.ajax({
			type: "GET",
			cache: false,
			dataType: "json",
			url: "FilteredOperations?service=" + selectedValue,
			async: false,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				
				if (response.ok) {
					var operationsMap = response.value;
					
					var targetSelect = $("#" + targetSelectId);
					targetSelect.html("");
					
					var operationsKeys = Object.keys(operationsMap);
					var optionsHtml = "";
					for(var i=0 ; i< operationsKeys.length ; i++) {
						var key = operationsKeys[i];
						var value = operationsMap[key];
						optionsHtml += "<option value='" + key + "'>" + value + "</option>";
						
					}
					
					targetSelect.html(optionsHtml);
					$("#" + targetSelectId).change();
					
					main.destroySelect(targetSelectId);
					
					if(isConfigsScreen) {
						$("#" + targetSelectId).selectmenu({width: 278});
					} else {
						$("#" + targetSelectId).selectmenu({width: 398});
					}
					
					main.registerSelectToDestroy(targetSelectId);
					

				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	}
};

$(main.initialLoading);
$(window).resize(main.resizeContent);