var list = {
	tableComponent: null,
	
	currentColumns: null,
	originalColumns: null,
	
	initialTableSource : null,
	
	dtConfigs: null,
	successCallback: null,
	aaData: null,
	
	tableContainerHeight: null,
	
	sumColumns: new Array(),
	sumColumnsFormatters: new Array(),
	
	defaultSort: [],
	
	detailsUrl: null,

	initDtConfigs: function() {
		list.dtConfigs =  $.parseJSON($("#dtConfigs").val());
		
		list.initialTableSource = list.dtConfigs.source;
		
		details.entityIdentifier = list.dtConfigs.entityIdentifier;
		details.entityIdName = list.dtConfigs.detailsIdentifier;
		details.entityName = list.dtConfigs.entityName;
		details.action = list.dtConfigs.detailsAction;
	},
	
	buildDataTable : function() {
		
		list.originalColumns = $.parseJSON($("#dtColumns").val());
		
		list.currentColumns = list.originalColumns;
		
		list.createTable();
	},
	
	buildExportToMailComponent: function() {
		$("#confirmExportToMail").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			open : function(event, ui) {
				$('.ui-widget-overlay').addClass("withOpacity");
			},
			close : function(event, ui) {
				$('.ui-widget-overlay').removeClass("withOpacity");
			}
		});
		
		main.registerConfigDialogToDestroy("confirmExportToMail");
		main.registerElementToDestroy($("#confirmExportToMail"));
		
		$("#confirmExportToMail button").button();
		
		search.addExtraInputComponent("exportBtn", auxTexts.exportToCSV, "button");
		
		$("#exportBtn").button({
			text : false,
			icons : {
				primary : "ui-icon-arrowthickstop-1-s"
			}
		});

		$("#exportBtn").unbind("click").click(function() {
			list.sendExportRequest(null);
		});
		$("#confirmMailCancelBtn").unbind("click").click(list.closeExportToMailDialog);
		$("#confirmMailOkBtn").unbind("click").click(list.configureMailAdressForExport);
	},
	
	openExportToMailDialog: function() {
		$("#confirmExportToMail").dialog("open");
	},
	
	closeExportToMailDialog: function() {
		$("#exportMailAddress").val("");
		$("#confirmExportToMail").dialog("close");
	},
	
	configureMailAdressForExport: function() {
		var mailAddr = $("#exportMailAddress").val();
		var regex = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
		var isValidMailAddress = main.validateInput(mailAddr, regex);
		
		if(isValidMailAddress) {
			main.showSuccess(auxTexts.sendExportToMail.replace("[email]", mailAddr));
			list.sendExportRequest(mailAddr);
		} else {
			main.showWarning(auxTexts.invalidMailAddress);
		}
		
	},
	
	sendExportRequest: function(mailAddr) {
		var requestUrl = search.currentSearchSource + "&exportToCSV=true";
		if(mailAddr != null) {
			requestUrl += "&mailAddress=" + mailAddr;
		}
		$.ajax({
			type: "GET",
			cache: false,
			url: requestUrl,
			dataType: "json",
			timeout: 70000,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response, status) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				if (!response.ok) {
					//first attempt to generate csv
					if(response.statusCode == "REPORT_GENERATION_TIMEOUT_ERROR" && mailAddr == null) {
						list.openExportToMailDialog();
					} else { //already sent export to mail request
						main.showWarning(response.statusMessage);
					}
				} else {
					if(mailAddr == null) {
						window.location = response.url;
					}
					list.closeExportToMailDialog();
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	createTable: function() {
		//(Re)creates table in dom
		var tableElementHtml = "<table id='mainDataTable' class='listGridTable'></table>";
		$("#mainDataTableContent").html(tableElementHtml);
		
		//Generates datatable
		list.tableComponent = $("#mainDataTable")
		.dataTable(
				{
					"oLanguage": {
		                "sUrl": "js/jquery.dataTables." + main.language + ".txt"
		            },
					"bJQueryUI" : true,
					"bInfo" : list.dtConfigs.showPaginationInfo,
					"sAjaxSource" : search.currentSearchSource,
					"bServerSide" : list.dtConfigs.allwaysFetchFromServer,
					"bScrollInfinite" : true,
					"bScrollCollapse" : true,
					"sScrollY" : list.getTableContentHeight(),
					"sScrollX" : "100%",
					"iDisplayLength" : list.dtConfigs.pageSize,
					"fnInfoCallback" : function(oSettings, iStart,
							iEnd, iMax, iTotal, sPre) {
						
						var extraText = "";
						
						for(var i=0 ; i<list.sumColumns.length ; i++) {
							var fieldName = list.sumColumns[i];
							var realIndex = list.tableComponent.fnGetColumnIndex(fieldName);
							var colData = list.tableComponent.fnGetColumnData(realIndex, false, false, false);
							var visibleIndex = list.tableComponent.fnColumnIndexToVisible(realIndex);
							var thTitle = "";
							
							if(visibleIndex != null) {
								//:nth-child selector is 1-indexed
								thTitle = $(".dataTables_scrollHead .dataTable").find("th:nth-child(" + (visibleIndex + 1) + ")").text();
							}
							
							var total = 0;
							for(var j=0 ; j<colData.length ; j++) {
								total += parseInt(colData[j], 10);
							}
							
							if(list.sumColumnsFormatters[i] != null) {
								total = list.sumColumnsFormatters[i](total);
							}
							
							
							extraText += "<b>" + thTitle + ": </b>" + total + "&nbsp;&nbsp;&nbsp;";
						}
						
						if(iTotal == 0) {
							return "0 - 0 / 0";
						} else if (iEnd == iTotal) {
							return "<div style='float:left;'>" + extraText + "</div><div>1 - "
									+ iEnd
									+ "&nbsp;&nbsp;&nbsp;&nbsp;/&nbsp;&nbsp;&nbsp;&nbsp;"
									+ iTotal + "</div>";
						} else {
							return "<div style='float:left;'>" + extraText + "</div><div>1 - "
									+ iEnd
									+ "&nbsp;&nbsp;&nbsp;&nbsp;/&nbsp;&nbsp;&nbsp;&nbsp;+"
									+ iEnd + "</div>";
						}
						
					},
					"aaSorting": list.defaultSort,
					"aoColumns" : list.currentColumns,
					"fnServerData" : function(sSource, aoData,
							fnCallback, oSettings) {
						oSettings.jqXHR = $.ajax({
							dataType: 'json',
							cache: false,
							type: "GET",
							url: sSource,
							data: aoData,
							timeout: 75000,
							success: function(jsonResponse, jqXHR) {
								if(main.isSessionExpiredOrGenericError(jsonResponse)) {
									return;
								}
								var extraConfs = "";
								search.activeFilters = search.menuOptionsComponent.find("tr.checked").find(".filterContent");
								if (search.activeFilters != null) {
									extraConfs = search.getConfigurationsForPersonalSearch();
								}
								
								if (header.applyFiltersAfterRedirect) {
									extraConfs = header.filtersToBeAppliedAfterRedirect;
								}
								
								//when the user clicks on a page and a populate is called, we do not want to run the redirect flow for browser navigation, so we set the flag to true
								//and we update the hash to the new location the user clicked so it is kept in the history stack by the browser
								//if the populate call is the same as the hash we do not want to change it because it triggers a new event and causes a loop
								if (Base64.decode(window.location.hash.replace("#","")) != sSource + extraConfs && sSource.indexOf("PlatformTecCommStatsListOfMessages") < 0) {
									header.ignoreHashChange = true;
									
									//currently this will store parameters sent on the url for the request and the extraConfs coming from the searchConf
									//the parameters of the url are redundant and being ignored here but are used on some other places, so do not remove for now 
									window.location.hash = Base64.encode(sSource + extraConfs);
									header.oldUrlforIE = window.location.hash;
									
								} else if (header.applyFiltersAfterRedirect) {
									search.parseSearchParamsAndApply(header.filtersToBeAppliedAfterRedirect.split("=")[1]);
									header.applyFiltersAfterRedirect = false;
								}
								
								if (!jsonResponse.ok) {
									main.showWarning(jsonResponse.statusMessage);
								} else {
									$("#mainDataTableContent").parent().show();
									fnCallback(jsonResponse);
									$("#mainDataTable_wrapper .dataTables_scrollBody").height(list.getTableContentHeight());
									
								}
								
								search.updateSearchConfigWithExtraFilters();
								list.updateColumnHeadersInfo();
								list.bindRowClickEvent();
								list.aaData = jsonResponse.aaData;
								
								if(list.successCallback != null && typeof list.successCallback === 'function') {
									list.successCallback();
								}
								
							},
							error: function(jqXHR, status, error) {
								if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
									return;
								}

								list.configureTableLoading();
							},
							complete: function() {
								main.hideLoading();
								main.hideTableLoading();
							}
						});
					},
					"fnDrawCallback" : function(oSettings) {
						$("#mainDataTable_wrapper .dataTables_scrollBody").height(list.getTableContentHeight());
						
						list.configureTableLoading();
					},
					"fnRowCallback": function( nRow, aData, iDisplayIndex ) {
						var tooltipText = "";
						if(list.dtConfigs.rowClickAction != null && list.dtConfigs.rowClickAction.trim() != "") {
							tooltipText = auxTexts.clickRowText;
						}
						
						
						if(list.dtConfigs.highlight != null && list.dtConfigs.highlight.trim() != "") {

							if (tooltipText.length > 0) {
								tooltipText += "\n";
							}
						
							var fn = transformFunctions[list.dtConfigs.highlight];
							tooltipText += fn(aData, nRow, tooltipText);
						}
						
						$("td", nRow).each(function() {
							var td = $(this);
							var currentTdContents = td.html();
							if(currentTdContents.length > 0 && currentTdContents.indexOf("<span>") != -1) {
								td.html(currentTdContents);
							} else {
								td.html("<span>" + currentTdContents + "</span>");
							}
						});
						
						$(nRow).attr("title", tooltipText);
						return nRow;
					}
				});
		
	},
	
	//Binds row click, if defined in xml
	bindRowClickEvent: function() {
		if((list.dtConfigs.detailsAction != null && list.dtConfigs.detailsAction.trim() != "") 
				|| list.dtConfigs.rowClickAction != null && list.dtConfigs.rowClickAction.length > 0) {
			$("#mainDataTable tbody tr").hover(function() {
				$(this).css("cursor", "pointer");
			});
			$("#mainDataTable tbody tr").unbind("click").click(function() {
				$("#mainDataTable tbody tr").each(function() {
					$(this).removeClass("selected");
				});
				$(this).addClass("selected");
				
				var index = list.tableComponent.fnGetPosition(this);
				var clickedOriginalRowData = list.tableComponent.fnGetData(index);
				var clickedFormattedRowData = list.tableComponent.fnGetNodes(index);

				if(list.dtConfigs.rowClickAction != null && list.dtConfigs.rowClickAction.length > 0) {
					var fn = main[list.dtConfigs.rowClickAction];
					fn(clickedOriginalRowData, clickedFormattedRowData, index);
					
				} else {
					var entityIdValue = clickedOriginalRowData[details.entityIdName];
					details.entityIdValue = entityIdValue;
					main.destroyDetailsDialogs();
					details.buildDetailsComponent(details.entityName + "Details");
					
				}
			});
		}
	},
	
	//Updates filter icon
	updateColumnHeadersInfo: function() {
		search.activeFilters = search.menuOptionsComponent.find("tr.checked").find(".filterContent");
		
		//Add filter icon in columns being filtered
		if(search.activeFilters != null) {
			search.activeFilters.each(function(index) {
				var td = $(this);
				var fieldValue = td.find("#fieldValue").val();
				if(fieldValue != null && fieldValue.length > 0) {
					var fieldName = td.find("#fieldName").val();
					var realIndex = list.tableComponent.fnGetColumnIndex(fieldName);
					var visibleIndex = list.tableComponent.fnColumnIndexToVisible(realIndex);
					
					if(visibleIndex != null) {
						//:nth-child selector is 1-indexed
						var th = $(".dataTables_scrollHead .dataTable").find("th:nth-child(" + (visibleIndex + 1) + ")");
						var div = th.find("div");
						div.find("span.ui-icon.ui-icon-heart").remove();
						div.append("<span class='ui-icon ui-icon-heart'></span>");
					}
				}
			});
		}
		
		//When table uses date filters, always add filter icon in the date column
		if(search.useDateSearch) {
			var realIndex = list.tableComponent.fnGetColumnIndex(search.dateFieldName);
			var visibleIndex = list.tableComponent.fnColumnIndexToVisible(realIndex);
			
			if(visibleIndex != null) {
				//:nth-child selector is 1-indexed
				var th = $(".dataTables_scrollHead .dataTable").find("th:nth-child(" + (visibleIndex + 1) + ")");
				var div = th.find("div");
				div.find("span.ui-icon.ui-icon-heart").remove();
				div.append("<span class='ui-icon ui-icon-heart'></span>");
			}
		}
	},
	
	configureTableLoading : function (){
		if($("#mainDataTable_wrapper") && $("#mainDataTable_wrapper").position()) {
			$("#tableLoading").css("top", $("#mainDataTable_wrapper").position().top);
			$("#tableLoading").css("left", $("#mainDataTable_wrapper").position().left);
			$("#tableLoading").height($("#mainDataTable_wrapper").height());
			$("#tableLoading").width($("#mainDataTable_wrapper").width());
		}
	},

	getTableContentHeight : function() {
		var headerHeight = $("#mainDataTable_wrapper .dataTables_scrollHead").height();
		var searchHeight = $("#extraOptions").height();
		var infoHeight = $(".dataTables_info").height();
		
		var tableContainerHeight = list.tableContainerHeight;
		
		if(list.tableContainerHeight == null) {
			tableContainerHeight = main.getContainerHeight();
		}
		
		if(headerHeight == null) {
			return tableContainerHeight - searchHeight - infoHeight - 20;
		} else {
			return tableContainerHeight - headerHeight - searchHeight - infoHeight - 20;
		}
	},
	
	getTableContentWidth: function() {
		return $("#body").width();
	},

	adjustWidth: function() {
		$("#mainDataTable").css("width", "100%");
		$("#mainDataTable_wrapper .dataTables_scrollHeadInner").width($("#mainDataTable").width());
		$("#mainDataTable_wrapper .dataTables_scrollHeadInner table").width($("#mainDataTable").width());
	},
	
	resizeContent : function() {
		$("#mainDataTable_wrapper .dataTables_scrollBody").height(list.getTableContentHeight());
		
		list.adjustWidth();
		
		list.configureTableLoading();
	}

};
$(window).resize(list.resizeContent);
