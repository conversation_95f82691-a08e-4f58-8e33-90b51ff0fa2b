var tecs = {
	salesEvoData: null,
	salesEvoDataView: null,
	salesEvoDataNumRecords: 0,
	qtyData: null,
	currencyData: null,
	chart: null,
	chartOptions: null,
	dialogInitialTopPosition: null,
	dialogInitialLeftPosition: null,
	
	timelineTable: null,
	salesEvoTable: null,
	
	confirmingTime: null,
	displayTimerHandler: null,
	checkTecStateTimerHandler: null,
	
	geoLimitContainer: null,
	
	targetEntityIdForTecTransfer: null,
	
	level2Elements: new Array(), //provinces
	level3Elements: new Array(), //municipalities
	level4Elements: new Array(), //sites
	sitesCounter: 0,
	
	tecsGeoLimits: null,
	
	tecGeofencedSites: null,
	
	buildDetailsContainer: function() {
		details.executeAfterFillDetailsScreen = tecs.executeAfterFillDetailsScreen;
		
		details.optionSelectChange = tecs.optionSelectChange;
		
	},
	
	buildFullDetailsComponents: function() {
		$("#debugMode").buttonset();
		
		$(".detailsContainer tr td:last-of-type div:empty").each(function() {
			var div = $(this);
			
			div.html("<i>" + auxTexts.unknowninfo + "</i>");
		});
		
		$("#chartEvolutionType").selectmenu({width: 105});
		main.registerSelectToDestroy("chartEvolutionType");
		
		$("#chartEvolutionType").change(tecs.changeChart);
		
		var locationButton = $("#location").next();
		
		locationButton.button({
			text : false,
			icons : {
				primary : "ui-icon-pin-s"
			}
		});
		
		var selectGeofencingAreaButton = $("#selectVisibleBtn");
		
		selectGeofencingAreaButton.button({
			text : false,
			icons : {
				primary : "ui-icon-contact"
			}
		});
		
		selectGeofencingAreaButton.click(tecs.selectGeofencingVisibleArea);
		
		tecs.buildGenerateTecAdminPinComponent();
		
		tecs.buildShowGeneratedTecAdminPinComponent();
		
		tecs.buildAddTecToBlacklistComponent();
		
		tecs.buildRemoveTecFromBlacklistComponent();
		
		tecs.buildResetTecAppVersionComponent();
		
		tecs.buildRemoveTecComponent();
		
		tecs.buildConfirmTecComponent();
		
		tecs.buildConfirmingTecComponent();
		
		tecs.buildActivateTecComponent();
		
		tecs.buildDeactivateTecComponent();
		
		tecs.buildDisassociateTecComponent();
		
		tecs.buildTransferTecComponent();
		
		tecs.buildMobileTecInstallationComponent();
		
		tecs.buildStartMsellerInstallationComponent();
		
		tecs.buildMobileTecReInstallationComponent();
		
		tecs.buildShowNewInstallPin7();
		
		tecs.buildAbortPendingUpdatesComponent();
		
		tecs.buildConfirmGeofencingEdit();
		
		tecs.buildConfirmGeofencingRemove();
		
		tecs.buildRestoreEntityComponent();
		
		tecs.buildConfirmUpdateIccid();
		
		tecs.buildConfirmTerminalReplace();
		
		tecs.buildConfirmSimReplace();
		
		tecs.buildRemoveTerminal();
	},
	
	optionSelectChange: function() {
		$("#detailsOptions").unbind("change");
		$("#detailsOptions").change(function(){
			var selectedOption = details.optionsSelectComponent.selectmenu("value");
			
			if (selectedOption == "salesstats") {
				if(!main.chartsLibsAlreadyLoaded) {
					main.loadChartsLibs(function() {
						tecs.getTecSalesEvolution(selectedOption);
					});
				} else {
					tecs.getTecSalesEvolution(selectedOption);
				}
			} else if (selectedOption == "timeline") {
				tecs.getTecTimeline(selectedOption);
			} else if (selectedOption == "generatetecadminpin") {
				tecs.openGenerateTecAdminPinDialog(selectedOption);
			} else if (selectedOption == "usagestats") {
				tecs.getTecUsageDetails(selectedOption);
			} else if (selectedOption == "mobiletecinstallation") {
				tecs.openMobileTecInstallationDialog();
			} else if (selectedOption == "startMsellerInstallation") {
				tecs.openStartMsellerInstallationDialog();
			} else if (selectedOption == "startMobileMsellerReInstallation") {
				tecs.openMobileTecReInstallationDialog();
			} else if (selectedOption == "addtectoblacklist") {
				tecs.openAddTecToBlacklistlDialog();
			} else if (selectedOption == "removetecfromblacklist") {
				tecs.openRemoveTecFromBlacklistlDialog();
			} else if (selectedOption == "resettecappversion") {
				tecs.openResetTecAppVersionDialog();
			} else if (selectedOption == "removetec") {
				tecs.openRemoveTecDialog();
			} else if (selectedOption == "confirmtec") {
				tecs.openConfirmTecDialog();
			} else if (selectedOption == "activatetec") {
				tecs.openActivateTecDialog();
			} else if (selectedOption == "deactivatetec") {
				tecs.openDeactivateTecDialog();
			} else if (selectedOption == "disassociatetec") {
				tecs.openDisassociateTecDialog();
			} else if (selectedOption == "transfertec") {
				tecs.getEntitiesForTecTransfer(selectedOption);
			} else if (selectedOption == "abortpendingupdates") {
				tecs.openAbortPendingUpdatesDialog();
			} else if (selectedOption == "restoreEntity") {
				tecs.openRestoreEntityDialog();
			} else if (selectedOption == "removeTerminal") {
				tecs.openRemoveTerminalDialog();
			}else if (selectedOption == "teceditsaleslimit") {
				tecs.loadSalesLimitData(selectedOption);
				
				var allButtons = [
				                  {text: auxTexts.buttonCancelText, click: details.closeDetailsDialog},
				                  {text: auxTexts.buttonApplyText, click: tecs.configureSalesLimitData}
				                  ];
     			details.showEditButtons(allButtons);
				
			} else if (selectedOption == "tecsetgeofencing") {
				details.showNewOptionContent(selectedOption);
				
				var allButtons = [
				                  {text: auxTexts.buttonCancelText, click: details.closeDetailsDialog},
				                  {text: auxTexts.buttonApplyGeofencingText, click: tecs.validateGeofencingSitesLimit},
				                  {text: auxTexts.buttonRemoveGeofencingText, click: tecs.openConfirmGeofencingRemoveDialog}
				                  ];
     			details.showEditButtons(allButtons);
				
				tecs.validateGeofencingInfo();
			} else if (selectedOption == "updateIccid") {
				tecs.loadUpdateIccidData(selectedOption);
				
				var allButtons = [
				                  {text: auxTexts.buttonCancelText, click: details.closeDetailsDialog},
				                  {text: auxTexts.buttonApplyText, click: tecs.validateUpdateIccidData}
				                  ];
     			details.showEditButtons(allButtons);
     			
			} else if (selectedOption == "terminalReplace") {
				tecs.loadTerminalReplaceData(selectedOption);
				
				var allButtons = [
				                  {text: auxTexts.buttonCancelText, click: details.closeDetailsDialog},
				                  {text: auxTexts.buttonApplyText, click: tecs.validateTerminalReplaceData}
				                  ];
     			details.showEditButtons(allButtons);
     			
			} else if (selectedOption == "simReplace") {
				tecs.loadSimReplaceData(selectedOption);
				
				var allButtons = [
				                  {text: auxTexts.buttonCancelText, click: details.closeDetailsDialog},
				                  {text: auxTexts.buttonApplyText, click: tecs.validateSimReplaceData}
				                  ];
     			details.showEditButtons(allButtons);
     			
			} else {
				details.showNewOptionContent(selectedOption);
			}
		});
	},
	
	executeAfterFillDetailsScreen: function() {
		var tecData = details.entityData.reply.tec;
		
		var detailsOptionsSelect = details.optionsSelectComponent;
		
		// Tec is deleted?
		if(tecData.deleted == true) {
			detailsOptionsSelect.find("option[value='editdata']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='removetec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='addtectoblacklist']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='removetecfromblacklist']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='usagestats']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='salesstats']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='timeline']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='mobiletecinstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMsellerInstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMobileMsellerReInstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='generatetecadminpin']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='resettecappversion']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='confirmtec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='activatetec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='deactivatetec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='disassociatetec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='transfertec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='teceditsaleslimit']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='abortpendingupdates']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='removeTerminal']").addClass("ui-state-hidden");
			
			//prevent restore mSeller from appearing if device is StoreAndroid
			if (tecData.device.deviceType == "StoreAndroid") {
				detailsOptionsSelect.find("option[value='restoreEntity']").addClass("ui-state-hidden");
			}			
			
			if(tecData.agentId == tecData.topHierarchyEntity) {
				$("#body_generaldata #subagent_agentId").hide();
			} else {
				$("#body_generaldata #subagent_agentId").show();
			}
			
			tecs.buildFullDetailsComponents();
			return;
		}
		detailsOptionsSelect.find("option[value='restoreEntity']").remove();

		// Toggle Blacklist
		if(tecData.blackListed == true) {
			detailsOptionsSelect.find("option[value='editdata']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='addtectoblacklist']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='usagestats']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='salesstats']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='timeline']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='mobiletecinstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMsellerInstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMobileMsellerReInstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='generatetecadminpin']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='resettecappversion']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='confirmtec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='activatetec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='deactivatetec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='disassociatetec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='transfertec']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='teceditsaleslimit']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='abortPendingUpdates']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='removeTerminal']").addClass("ui-state-hidden");
			
			if(tecData.agentId == tecData.topHierarchyEntity) {
				$("#body_generaldata #subagent_agentId").hide();
			} else {
				$("#body_generaldata #subagent_agentId").show();
			}
			
			tecs.buildFullDetailsComponents();
			return;
			
		} else {
			detailsOptionsSelect.find("option[value='removetecfromblacklist']").addClass("ui-state-hidden");
		}
		
		if(tecData.tecType != "SALES_TEC") {
			detailsOptionsSelect.find("option[value='salesstats']").remove();
			detailsOptionsSelect.find("option[value='mobiletecinstallation']").remove();
			detailsOptionsSelect.find("option[value='startMsellerInstallation']").remove();
			detailsOptionsSelect.find("option[value='startMobileMsellerReInstallation']").remove();
			detailsOptionsSelect.find("option[value='transfertec']").remove();
			detailsOptionsSelect.find("option[value='teceditsaleslimit']").remove();
			detailsOptionsSelect.find("option[value='tecsetgeofencing']").remove();
		}
		
		if(tecData.tecState != "ASSOCIATED") {
			detailsOptionsSelect.find("option[value='salesstats']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='terminalReplace']").removeClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='simReplace']").removeClass("ui-state-hidden");
		} else {
			detailsOptionsSelect.find("option[value='salesstats']").removeClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='editdata']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='terminalReplace']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='simReplace']").addClass("ui-state-hidden");
		}
		
		// Mobile TEC install
		if(tecData.device.deviceType == "TEC") {
			detailsOptionsSelect.find("option[value='mobiletecinstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMsellerInstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMobileMsellerReInstallation']").addClass("ui-state-hidden");
			
		} else {
			detailsOptionsSelect.find("option[value='mobiletecinstallation']").removeClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMsellerInstallation']").removeClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMobileMsellerReInstallation']").removeClass("ui-state-hidden");
		}

		
		// Reset TEC app version
		if(tecData.tecType == "SALES_TEC" && tecData.tecVersion.versionId != 0) {
			detailsOptionsSelect.find("option[value='resettecappversion']").removeClass("ui-state-hidden");
		} else {
			detailsOptionsSelect.find("option[value='resettecappversion']").addClass("ui-state-hidden");
		}
		
		// Set all state actions to hiden
		detailsOptionsSelect.find("option[value='removetec']").addClass("ui-state-hidden");
		detailsOptionsSelect.find("option[value='confirmtec']").addClass("ui-state-hidden");
		detailsOptionsSelect.find("option[value='activatetec']").addClass("ui-state-hidden");
		detailsOptionsSelect.find("option[value='deactivatetec']").addClass("ui-state-hidden");
		detailsOptionsSelect.find("option[value='disassociatetec']").addClass("ui-state-hidden");
		detailsOptionsSelect.find("option[value='transfertec']").addClass("ui-state-hidden");
		detailsOptionsSelect.find("option[value='removeTerminal']").addClass("ui-state-hidden");
		
		// Remove TEC
		if (tecData.tecState == "CREATED" ||
				tecData.tecState == "CONFIRMED" ||
				tecData.tecState == "INACTIVE") {
			//only show remove if tec is TEC, mSeller Legacy or USSD
			if(tecData.device.deviceType == "TEC" || tecData.device.deviceType == "ANDROID"
				|| tecData.device.deviceType == "PARTNERAPI"){
				detailsOptionsSelect.find("option[value='removetec']").removeClass("ui-state-hidden");
			}
		}

		// Confirm TEC
		if (tecData.tecState == "CREATED") {
			detailsOptionsSelect.find("option[value='confirmtec']").removeClass("ui-state-hidden");
		}
		
		// Activate TEC
		if (tecData.tecState == "CONFIRMED" ||
				tecData.tecState == "INACTIVE") {
			detailsOptionsSelect.find("option[value='activatetec']").removeClass("ui-state-hidden");
		}
		
		// Deactivate TEC
		if (tecData.tecState == "ACTIVE") {
			detailsOptionsSelect.find("option[value='deactivatetec']").removeClass("ui-state-hidden");
		}
		
		// Sales Tecs
		if (tecData.tecType == "SALES_TEC" &&
				(tecData.tecState == "ASSOCIATED" ||
				tecData.tecState == "INACTIVE" ||
				tecData.tecState == "ASSOCIATING_STORE" ||
				tecData.tecState == "CREATED")) {
			
			//transfertec
			detailsOptionsSelect.find("option[value='transfertec']").removeClass("ui-state-hidden");
			
			//only show remove if tec is StoreAndroid or USSD
			if(tecData.device.deviceType == "StoreAndroid" || tecData.device.deviceType == "USSD"){
				detailsOptionsSelect.find("option[value='removeTerminal']").removeClass("ui-state-hidden");
				}
		}
		
		//Only TECs or mSeller legacy are allowed to be disassociated
		if (tecData.tecType == "SALES_TEC" && (tecData.device.deviceType == "TEC" || tecData.device.deviceType == "Android")
				&& tecData.tecState == "ASSOCIATED"){
			detailsOptionsSelect.find("option[value='disassociatetec']").removeClass("ui-state-hidden");
		}
		
		// Has pending updates
		if (tecData.hasPendingUpdates == false) {
			detailsOptionsSelect.find("option[value='abortPendingUpdates']").addClass("ui-state-hidden");
		}
		
		//StoreAndroid and USSD
		if(tecData.device.deviceType == "StoreAndroid" || tecData.device.deviceType == "USSD"){
			detailsOptionsSelect.find("option[value='abortpendingupdates']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMsellerInstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='startMobileMsellerReInstallation']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='mobiletecinstallation']").addClass("ui-state-hidden");
			}
		
		//USSD
		if(tecData.device.deviceType == "USSD"){
			detailsOptionsSelect.find("option[value='generatetecadminpin']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='resettecappversion']").addClass("ui-state-hidden");
			detailsOptionsSelect.find("option[value='updateIccid']").addClass("ui-state-hidden");
			}
		
		if(tecData.agentId == tecData.topHierarchyEntity) {
			$("#body_generaldata #subagent_agentId").hide();
		} else {
			$("#body_generaldata #subagent_agentId").show();
		}
		
		if(tecData.tecType == "SALES_TEC") {
			$("#lastOperationLabel").html(auxTexts.lastSale);
		} else {
			$("#lastOperationLabel").html(auxTexts.lastInstall);
		}
		
		//Partner API?
		if(tecData.device.deviceType == 'PartnerAPI'){
			detailsOptionsSelect.find("option[value='transfertec']").addClass("ui-state-hidden");
		}
		tecs.buildFullDetailsComponents();
	},
	
	buildConfirmUpdateIccid: function() {
		$("#confirmUpdateIccid").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
		});
		
		$("#confirmUpdateIccid button").button();
		
		main.registerDetailsDialogToDestroy("confirmUpdateIccid");
		
		$("#confirmUpdateIccidCancelBtn").unbind("click").click(tecs.closeConfirmUpdateIccidDialog);
		$("#confirmUpdateIccidOkBtn").unbind("click").click(tecs.confirmUpdateIccidData);
	},
	
	buildConfirmTerminalReplace: function() {
		$("#confirmTerminalReplace").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
		});
		
		$("#confirmTerminalReplace button").button();
		
		main.registerDetailsDialogToDestroy("confirmTerminalReplace");
		
		$("#confirmTerminalReplaceCancelBtn").unbind("click").click(tecs.closeConfirmTerminalReplaceDialog);
		$("#confirmTerminalReplaceOkBtn").unbind("click").click(tecs.confirmTerminalReplaceData);
	},
	
	buildConfirmSimReplace: function() {
		$("#confirmSimReplace").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
		});
		
		$("#confirmSimReplace button").button();
		
		main.registerDetailsDialogToDestroy("confirmSimReplace");
		
		$("#confirmSimReplaceCancelBtn").unbind("click").click(tecs.closeConfirmSimReplaceDialog);
		$("#confirmSimReplaceOkBtn").unbind("click").click(tecs.confirmSimReplace);
	},
	
	buildAddTecToBlacklistComponent: function() {
		$("#addTecToBlacklist").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#addTecToBlacklist button").button();
		
		main.registerDetailsDialogToDestroy("addTecToBlacklist");
		
		$("#confirmAddTecToBlacklistCancelBtn").unbind("click").click(tecs.closeAddTecToBlacklistDialog);
		$("#confirmAddTecToBlacklistOkBtn").unbind("click").click(tecs.configureAddTecToBlacklistReason);
	},
	
	openAddTecToBlacklistlDialog: function() {
		$("#addTecToBlacklist").dialog("open");
	},
	
	closeAddTecToBlacklistDialog: function() {
		$("#addTecToBlacklistReason").val("");
		$("#addTecToBlacklist").dialog("close");
	},
	
	configureAddTecToBlacklistReason: function() {
		var reason = $("#addTecToBlacklistReason").val();
		var regex = /^.{1,100}$/;
		var isReasonValid = main.validateInput(reason, regex);
		
		if(isReasonValid) {
			tecs.sendAddTecToBlacklistRequest();
		} else {
			main.showWarning(auxTexts.invalidReason);
		}
	},
	
	sendAddTecToBlacklistRequest: function() {
		var requestUrl = "AddTecToBlacklist?" + details.entityIdName + "=" + details.entityIdValue;

		var dataToSend = $("#addTecToBlacklistForm").serialize();
		
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			url: requestUrl,
			data: dataToSend,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				tecs.closeAddTecToBlacklistDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					details.reloadDetailsDialog();
					main.showSuccess(response.statusMessage);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildRemoveTecFromBlacklistComponent: function() {
		$("#removeTecFromBlacklist").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#removeTecFromBlacklist button").button();
		
		main.registerDetailsDialogToDestroy("removeTecFromBlacklist");
		
		$("#confirmRemoveTecFromBlacklistCancelBtn").unbind("click").click(tecs.closeRemoveTecFromBlacklistDialog);
		$("#confirmRemoveTecFromBlacklistOkBtn").unbind("click").click(tecs.configureRemoveTecFromBlacklistReason);
	},
	
	openRemoveTecFromBlacklistlDialog: function() {
		$("#removeTecFromBlacklist").dialog("open");
	},
	
	closeRemoveTecFromBlacklistDialog: function() {
		$("#removeTecFromBlacklistReason").val("");
		$("#removeTecFromBlacklist").dialog("close");
	},
	
	configureRemoveTecFromBlacklistReason: function() {
		var reason = $("#removeTecFromBlacklistReason").val();
		var regex = /^.{1,100}$/;
		var isReasonValid = main.validateInput(reason, regex);
		
		if(isReasonValid) {
			tecs.sendRemoveTecFromBlacklistRequest();
		} else {
			main.showWarning(auxTexts.invalidReason);
		}
	},
	
	sendRemoveTecFromBlacklistRequest: function() {
		var requestUrl = "RemoveTecFromBlacklist?" + details.entityIdName + "=" + details.entityIdValue;
		var dataToSend = $("#removeTecFromBlacklistForm").serialize();
		
		$.ajax({
			type: "POST",
			cache: false,
			url: requestUrl,
			dataType: "json",
			data: dataToSend,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				tecs.closeRemoveTecFromBlacklistDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					details.reloadDetailsDialog();
					main.showSuccess(response.statusMessage);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildMobileTecInstallationComponent: function() {
		$("#mobileTecInstallation").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#mobileTecInstallation button").button();
		
		main.registerDetailsDialogToDestroy("mobileTecInstallation");
		
		$("#confirmMobileTecInstallationCancelBtn").unbind("click").click(tecs.closeMobileTecInstallationDialog);
		$("#confirmMobileTecInstallationOkBtn").unbind("click").click(tecs.sendMobileTecInstallationRequest);
	},
	
	openMobileTecInstallationDialog: function() {
		$("#mobileTecInstallation").dialog("open");
	},
	
	closeMobileTecInstallationDialog: function() {
		$("#mobileTecInstallation").dialog("close");
	},
	
	sendMobileTecInstallationRequest: function() {
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			url: "MobileTecConfirmation?" + details.entityIdName + "=" + details.entityIdValue,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				tecs.closeMobileTecInstallationDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					details.getDetailsFromEntity(details.entityIdValue);
					main.showSuccess(response.statusMessage);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildMobileTecReInstallationComponent: function() {
		$("#startMobileMsellerReInstallation").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#startMobileMsellerReInstallation button").button();
		
		main.registerDetailsDialogToDestroy("startMobileMsellerReInstallation");
		
		$("#startMobileMsellerReInstallationCancelBtn").unbind("click").click(tecs.closeMobileTecReInstallationDialog);
		$("#startMobileMsellerReInstallationOkBtn").unbind("click").click(tecs.sendMobileTecReInstallationRequest);
	},
	
	openMobileTecReInstallationDialog: function() {
		$("#startMobileMsellerReInstallation").dialog("open");
	},
	
	closeMobileTecReInstallationDialog: function() {
		$("#startMobileMsellerReInstallation").dialog("close");
	},
	
	sendMobileTecReInstallationRequest: function(callback) {
		
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			url: "MobileTecReInstallation?" + details.entityIdName + "=" + details.entityIdValue,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				
				tecs.closeMobileTecReInstallationDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.openShowNewInstallPin7(response);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildShowNewInstallPin7: function() {
		$("#showNewInstallPin7").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#showNewInstallPin7 button").button();
		
		main.registerDetailsDialogToDestroy("showNewInstallPin7");
		$("#generatedNewPin7OkBtn").unbind("click").click(tecs.closeShowNewInstallPin7);
	},
	
	openShowNewInstallPin7: function(response) {
		
		$("#showNewInstallPin7").dialog("open");
		$("#generatedNewPin7").html(tokenText.generatedNewPin7 + ": " + response.pin);
		
		var expireDate = new Date(response.expireDate).toISOString().substring(0,16).replace("T", " ")
		$("#expireDateNewPin7").html(tokenText.expireDateNewPin7 + ": " + expireDate);
	},
	
	closeShowNewInstallPin7: function() {
		$("#generatedNewPin7").html("");
		$("#expireDateNewPin7").html("");
		$("#showNewInstallPin7").dialog("close");
	},
	
	buildStartMsellerInstallationComponent: function() {
		$("#startMsellerInstallation").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#startMsellerInstallation button").button();
		
		main.registerDetailsDialogToDestroy("startMsellerInstallation");
		
		$("#confirmStartMsellerInstallationCancelBtn").unbind("click").click(tecs.closeStartMsellerInstallationDialog);
		$("#confirmStartMsellerInstallationOkBtn").unbind("click").click(tecs.sendStartMsellerInstallationRequest);
	},
	
	openStartMsellerInstallationDialog: function() {
		$("#startMsellerInstallation").dialog("open");
	},
	
	closeStartMsellerInstallationDialog: function() {
		$("#startMsellerInstallation").dialog("close");
	},
	
	sendStartMsellerInstallationRequest: function() {
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			url: "StartMsellerInstallation?" + details.entityIdName + "=" + details.entityIdValue,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}

				tecs.closeStartMsellerInstallationDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					details.getDetailsFromEntity(details.entityIdValue);
					main.showSuccess(response.statusMessage);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildAbortPendingUpdatesComponent: function() {
		$("#abortPendingUpdates").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#abortPendingUpdates button").button();
		
		main.registerDetailsDialogToDestroy("abortPendingUpdates");
		
		$("#confirmAbortPendingUpdatesCancelBtn").unbind("click").click(tecs.closeAbortPendingUpdatesDialog);
		$("#confirmAbortPendingUpdatesOkBtn").unbind("click").click(tecs.sendAbortPendingUpdatesRequest);
	},
	
	openAbortPendingUpdatesDialog: function() {
		$("#abortPendingUpdates").dialog("open");
	},
	
	closeAbortPendingUpdatesDialog: function() {
		$("#abortPendingUpdates").dialog("close");
	},
	
	sendAbortPendingUpdatesRequest: function() {
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			url: "AbortPendingUpdates?" + details.entityIdName + "=" + details.entityIdValue,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
							
				tecs.closeAbortPendingUpdatesDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					details.getDetailsFromEntity(details.entityIdValue);
					main.showSuccess(response.statusMessage);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildResetTecAppVersionComponent: function() {
		$("#resetTecAppVersion").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#resetTecAppVersion button").button();
		
		main.registerDetailsDialogToDestroy("resetTecAppVersion");
		
		$("#confirmResetTecAppVersionCancelBtn").unbind("click").click(tecs.closeResetTecAppVersionDialog);
		$("#confirmResetTecAppVersionOkBtn").unbind("click").click(tecs.sendResetTecAppVersionRequest);
	},
	
	openResetTecAppVersionDialog: function() {
		$("#resetTecAppVersion").dialog("open");
	},
	
	closeResetTecAppVersionDialog: function() {
		$("#resetTecAppVersion").dialog("close");
	},
	
	sendResetTecAppVersionRequest: function() {
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			url: "ResetTecAppVersion?" + details.entityIdName + "=" + details.entityIdValue,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				tecs.closeResetTecAppVersionDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					main.showSuccess(response.statusMessage);
					details.getDetailsFromEntity(details.entityIdValue);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},

	buildRemoveTecComponent: function() {
		$("#removeTec").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#removeTec button").button();
		
		main.registerDetailsDialogToDestroy("removeTec");
		
		$("#confirmRemoveTecCancelBtn").unbind("click").click(tecs.closeRemoveTecDialog);
		$("#confirmRemoveTecOkBtn").unbind("click").click(tecs.configureRemoveTecReason);
	},
	
	configureRemoveTecReason: function() {
		var reason = $("#removeTecReason").val();
		var regex = /^.{1,100}$/;
		var isReasonValid = main.validateInput(reason, regex);
		
		if(isReasonValid) {
			tecs.sendRemoveTecRequest();
		} else {
			main.showWarning(auxTexts.invalidReason);
		}
	},
		
	openRemoveTecDialog: function() {
		$("#removeTec").dialog("open");
	},
	
	closeRemoveTecDialog: function() {
		$("#removeTecReason").val("");
		$("#removeTec").dialog("close");
	},
	
	sendRemoveTecRequest: function() {
		var requestUrl = "RemoveTec?" + details.entityIdName + "=" + details.entityIdValue;
		var dataToSend = $("#removeTecForm").serialize();
		
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			data: dataToSend,
			url: requestUrl,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				tecs.closeRemoveTecDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					search.applyConfigurations();
					details.closeDetailsDialog();
					main.showSuccess(response.statusMessage);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildConfirmTecComponent: function() {
		$("#confirmTec").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#confirmTec button").button();
		
		main.registerDetailsDialogToDestroy("confirmTec");
		
		$("#confirmConfirmTecCancelBtn").unbind("click").click(tecs.closeConfirmTecDialog);
		$("#confirmConfirmTecOkBtn").unbind("click").click(tecs.configureConfirmTecReason);
	},
	
	configureConfirmTecReason: function() {
		var reason = $("#confirmTecReason").val();
		var regex = /^.{1,100}$/;
		var isReasonValid = main.validateInput(reason, regex);
		
		if(isReasonValid) {
			tecs.sendConfirmTecRequest();
		} else {
			main.showWarning(auxTexts.invalidReason);
		}
	},
	
	openConfirmTecDialog: function() {
		$("#confirmTec").dialog("open");
		
	},
	
	closeConfirmTecDialog: function() {
		$("#confirmTecReason").val("");
		$("#confirmTec").dialog("close");
	},
	
	sendConfirmTecRequest: function() {
		var requestUrl = "ConfirmTec?" + details.entityIdName + "=" + details.entityIdValue;
		var dataToSend = $("#confirmTecForm").serialize();
		
		$.ajax({
			type: "POST",
			cache: false,
			url: requestUrl,
			dataType: "json",
			data: dataToSend,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				tecs.closeConfirmTecDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.openConfirmingTecDialog();
					$("#confirmingPin").html(auxTexts.confirmingTecPin + ": " + response.encryptedPin);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildConfirmingTecComponent: function() {
		$("#confirmingTec").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
				// Stop timers
				clearInterval(tecs.checkTecStateTimerHandler);
				clearInterval(tecs.displayTimerHandler);
				
				details.getDetailsFromEntity(details.entityIdValue);
			}
		});
		
		$("#confirmingTec button").button();
		
		main.registerDetailsDialogToDestroy("confirmingTec");
		
		$("#confirmConfirmingTecOkBtn").unbind("click").click(tecs.closeConfirmingTecDialog);
	},
	
	openConfirmingTecDialog: function() {
		$("#confirmingTec").dialog("open");
		
		tecs.confirmingTime = 5 * 60 * 1000;
		tecs.displayTimer();
		
		// Start timers
		tecs.checkTecStateTimerHandler = setInterval(tecs.checkTecStateTimer, 5000);
		tecs.displayTimerHandler = setInterval(tecs.displayTimer, 1000);
	},
	
	closeConfirmingTecDialog: function() {
		$("#confirmingTec").dialog("close");
	},
	
	displayTimer: function() {
		if(tecs.confirmingTime > 0) {
			var secondsLeft = tecs.confirmingTime / 1000;
			
			// do some time calculations
			var minutes = parseInt(secondsLeft / 60);
			var seconds = parseInt(secondsLeft % 60);
			
			// Format time
			minutes = ( minutes < 10 ? "0" : "" ) + minutes;
			seconds = ( seconds < 10 ? "0" : "" ) + seconds;
			
			// Get formatted string
			var countDown = minutes + ":" + seconds;
			$("#confirmingTimer").html(auxTexts.confirmingTecTime + ": " + countDown);
		} else {
			tecs.closeConfirmingTecDialog();
			main.showWarning(auxTexts.confirmingTecExpired);
		}

		// Find the amount of seconds between now and target
		tecs.confirmingTime -= 1000;
	},
	
	checkTecStateTimer: function() {
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			url: "GetTecState?" + details.entityIdName + "=" + details.entityIdValue,
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
							
				if (response.ok) {
					// TEC successfully confirmed?
					if (response.tecState == "CONFIRMED") {
						tecs.closeConfirmingTecDialog();
						main.showSuccess(response.statusMessage);
					}
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},

	buildActivateTecComponent: function() {
		$("#activateTec").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#activateTec button").button();
		
		main.registerDetailsDialogToDestroy("activateTec");
		
		$("#confirmActivateTecCancelBtn").unbind("click").click(tecs.closeActivateTecDialog);
		$("#confirmActivateTecOkBtn").unbind("click").click(tecs.configureActivateTecReason);
	},
	
	configureActivateTecReason: function() {
		var reason = $("#activateTecReason").val();
		var regex = /^.{1,100}$/;
		var isReasonValid = main.validateInput(reason, regex);
		
		if(isReasonValid) {
			tecs.sendActivateTecRequest();
		} else {
			main.showWarning(auxTexts.invalidReason);
		}
	},
	
	openActivateTecDialog: function() {
		$("#activateTec").dialog("open");
	},
	
	closeActivateTecDialog: function() {
		$("#activateTecReason").val("");
		$("#activateTec").dialog("close");
	},
	
	sendActivateTecRequest: function() {
		var requestUrl = "ActivateTec?" + details.entityIdName + "=" + details.entityIdValue;
		var dataToSend = $("#activateTecForm").serialize();
		
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			data: dataToSend,
			url: requestUrl,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				tecs.closeActivateTecDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					main.showSuccess(response.statusMessage);
					details.getDetailsFromEntity(details.entityIdValue);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},

	buildDeactivateTecComponent: function() {
		$("#deactivateTec").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#deactivateTec button").button();
		
		main.registerDetailsDialogToDestroy("deactivateTec");
		
		$("#confirmDeactivateTecCancelBtn").unbind("click").click(tecs.closeDeactivateTecDialog);
		$("#confirmDeactivateTecOkBtn").unbind("click").click(tecs.configureDeactivateTecReason);
	},
	
	configureDeactivateTecReason: function() {
		var reason = $("#deactivateTecReason").val();
		var regex = /^.{1,100}$/;
		var isReasonValid = main.validateInput(reason, regex);
		
		if(isReasonValid) {
			tecs.sendDeactivateTecRequest();
		} else {
			main.showWarning(auxTexts.invalidReason);
		}
	},

	openDeactivateTecDialog: function() {
		$("#deactivateTec").dialog("open");
	},
	
	closeDeactivateTecDialog: function() {
		$("#deactivateTecReason").val("");
		$("#deactivateTec").dialog("close");
	},
	
	sendDeactivateTecRequest: function() {
		var requestUrl = "DeactivateTec?" + details.entityIdName + "=" + details.entityIdValue;
		
		var dataToSend=$("#deactivateTecForm").serialize();
		
		$.ajax({
			type: "POST",
			cache: false,
			url: requestUrl,
			dataType: "json",
			data: dataToSend,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				tecs.closeDeactivateTecDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					main.showSuccess(response.statusMessage);
					details.getDetailsFromEntity(details.entityIdValue);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},

	buildDisassociateTecComponent: function() {
		$("#disassociateTec").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#disassociateTec button").button();
		
		main.registerDetailsDialogToDestroy("disassociateTec");
		
		$("#confirmDisassociateTecCancelBtn").unbind("click").click(tecs.closeDisassociateTecDialog);
		$("#confirmDisassociateTecOkBtn").unbind("click").click(tecs.configureDisassociateTecReason);
	},
	
	configureDisassociateTecReason: function() {
		var reason = $("#disassociateTecReason").val();
		var regex = /^.{1,100}$/;
		var isReasonValid = main.validateInput(reason, regex);
		
		if(isReasonValid) {
			tecs.sendDisassociateTecRequest();
		} else {
			main.showWarning(auxTexts.invalidReason);
		}
	},
	
	openDisassociateTecDialog: function() {
		$("#disassociateTec").dialog("open");
	},
	
	closeDisassociateTecDialog: function() {
		$("#disassociateTecReason").val("");
		$("#disassociateTec").dialog("close");
	},
	
	sendDisassociateTecRequest: function() {
		var requestUrl = "DisassociateTec?" + details.entityIdName + "=" + details.entityIdValue;
		var dataToSend = $("#disassociateTecForm").serialize();
		
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			data: dataToSend,
			url: requestUrl,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				tecs.closeDisassociateTecDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					main.showSuccess(response.statusMessage);
					details.getDetailsFromEntity(details.entityIdValue);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	//
	buildRemoveTerminal : function() {
		$("#confirmRemoveTerminal").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width : 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : {
				my : "top",
				at : "top+5",
				of : details.detailsComponent
			},
		});

		$("#confirmRemoveTerminal button").button();

		main.registerDetailsDialogToDestroy("confirmRemoveTerminal");

		$("#confirmRemoveTerminalCancelBtn").unbind("click").click(
				tecs.closeRemoveTerminalDialog);
		$("#confirmRemoveTerminalOkBtn").unbind("click").click(
				tecs.sendRemoveTerminalRequest);
	},

	openRemoveTerminalDialog : function() {
		$("#confirmRemoveTerminal").dialog("open");
	},

	closeRemoveTerminalDialog : function() {
		$("#confirmRemoveTerminal").dialog("close");
	},

	sendRemoveTerminalRequest : function() {
		$.ajax({
			type : "POST",
			cache : false,
			dataType : "json",
			url : "ConfirmRemoveTerminal?" + details.entityIdName + "="
					+ details.entityIdValue,
			beforeSend : function() {
				main.showOperationsLoading();
			},
			complete : function() {
				main.hideOperationsLoading();
			},
			success : function(response) {
				if (main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}

				tecs.closeRemoveTerminalDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					details.getDetailsFromEntity(details.entityIdValue);
					main.showSuccess(response.statusMessage);
				}
			},
			error : function(jqXHR, status, error) {
				if (main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	//
	
	applyLocationStyles: function() {
		var div = $("#location");
		var value = div.text();
		var address = value;
		
		if(value != null && value.trim().length > 0) {
			
			div.width(div.parent().width() - 48);
			
			$.ajax({
				type: "GET",
				dataType: "json",
				cache: false,
				url: "http://maps.googleapis.com/maps/api/geocode/json?latlng=" + value + "&sensor=false" ,
				success: function(response) {
					if(main.isSessionExpiredOrGenericError(response, null)) {
						return;
					}

					if(response.status == "OK") {
						if(response.results != null && response.results.length > 0) {
							address = response.results[0].formatted_address;
							if(address != null && address.trim().length > 0) {
								div.text(address);
							}
						}
					}
				},
				error: function(jqXHR, status, error) {
					if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
						return;
					}
				}
			});
			
			var locationButton = $("#location").next();
			locationButton.unbind("click");
			locationButton.click(function() {
				main.showLocationOnMap(value, address);
			});
		} 
	},

	getTecUsageDetails: function(selectedOption) {
		$.ajax({
			type: "GET",
			cache: false,
			dataType: "json",
			url: "TecDetails?" + details.entityIdName + "=" + details.entityIdValue,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				if (!response.reply.ok) {
					main.showWarning(response.reply.statusMessage);
				} else {
					tecs.fillUsageDetails(response.reply);
					details.showNewOptionContent(selectedOption);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	fillUsageDetails: function(usageData) {
		$("#lastSms").html(main.transformTimestampValueInDateTimeText(usageData.tecStats.lastActivityDate, formats.dateComplete));
		$("#lastBoot").html(main.transformTimestampValueInDateTimeText(usageData.tecStats.lastBootDate, formats.dateComplete));
		$("#lastLogin").html(main.transformTimestampValueInDateTimeText(usageData.tecStats.lastLoginDate, formats.dateComplete));
		$("#lastOperation").html(main.transformTimestampValueInDateTimeText(usageData.tecStats.lastOperationDate, formats.dateComplete));
		
		if(usageData.tecStats.locationLatitude && usageData.tecStats.locationLongitude) {
			$("#location").next().show();
			$("#location").html(usageData.tecStats.locationLatitude + "," + usageData.tecStats.locationLongitude);
			tecs.applyLocationStyles();
		} else {
			$("#location").html("<i>" + auxTexts.unknowninfo + "</i>");
			$("#location").next().hide();
		}
	},
	
	getTecTimeline: function(selectedOption) {
		$.ajax({
			type: "GET",
			cache: false,
			url: "TecTimeline",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpired(response)) {
					return;
				} 
				
				main.destroyInnerListComponents();
				main.destroyInnerComponents();
				$("#tblTimelineStats").html(response);
				
				innerSearch.extraSearchFilters = new Array();
				innerSearch.extraSearchFilters["tecId"] = details.entityIdValue;
				
				innerList.tableContainerWidth = details.detailsComponent.width();
				
				innerSearch.buildFiltersAndInitializeDataTable();
				
				innerSearch.buildConfigurationMenu();
				
				$("#innerExtraOptions button.advancedSearchMenuBtn").hide();
				
				main.registerInnerListToDestroy("tblTimelineStats");
				details.showNewOptionContent(selectedOption);
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},

	getTecSalesEvolution: function(selectedOption) {
		$.ajax({
			type: "GET",
			dataType: "json",
			cache: false,
			url: "TecSalesEvolution?" + details.entityIdName + "=" + details.entityIdValue,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}				
				if (!response.ok) {
					main.showWarning(response.statusMessage);		
				} else {
					tecs.drawChartAndTable(response.records);
					details.showNewOptionContent(selectedOption);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},

	drawChartAndTable: function(records) {
		
		$("#showhideStoreSeries").button({
			text : false,
			icons : {
			primary : "ui-icon-home"
		}
		});
		$("#showhideStoreSeries").change(function(event) {
			var active = event.target.checked;
			if(active == true) {
				// show store series
				tecs.salesEvoDataView.setColumns([0,1,2]);
			} else {
				// hide store series
				tecs.salesEvoDataView.setColumns([0,1]);
			}
			
			// update the chart
			tecs.chart.draw(tecs.salesEvoDataView, tecs.chartOptions);
		});
		
		// google chart data
		tecs.salesEvoData = new google.visualization.DataTable();
		tecs.salesEvoData.addColumn('date', salesEvoLabels.date, "date");
		tecs.salesEvoData.addColumn('number', salesEvoLabels.tec, "tecvalue");
		tecs.salesEvoData.addColumn('number', salesEvoLabels.store, "storevalue");
		
		// TEC totals to display in the table
		var todayQty = 0, todayCredit = 0, todayCurrency = 0,
			yesterdayQty = 0, yesterdayCredit = 0, yesterdayCurrency = 0,
			lastSevenDaysQty = 0, lastSevenDaysCredit = 0, lastSevenDaysCurrency = 0,
			lastMonthQty = 0, lastMonthCredit = 0, lastMonthCurrency = 0;

		var today = moment().startOf('day');
		var yesterday = moment().startOf('day').subtract('days', 1);
		var sevenDaysAgo = moment().startOf('day').subtract('days', 7);
		var thirtyDaysAgo = moment().startOf('day').subtract('days', 30);

		// calculate and group the data according to the above intervals for the tec
   		$.each(records, function(i, obj) {
   			
   			var date = new Date(obj.date);
   			var tecQty = obj.tecTotals.totalSalesQuantity;
   			var tecCredit = obj.tecTotals.totalSalesPoints;
   			var tecCurrency = obj.tecTotals.totalSalesCurrency;
   			
   			if (today.isSame(date)) {
   				todayQty += tecQty;
   				todayCredit += tecCredit;
   				todayCurrency += tecCurrency;
   			} 

   			if (yesterday.isSame(date)) {
   				yesterdayQty += tecQty;
   				yesterdayCredit += tecCredit;
   				yesterdayCurrency += tecCurrency;
   			} 

   			if (sevenDaysAgo.isBefore(date) || sevenDaysAgo.isSame(date)) {
   				lastSevenDaysQty += tecQty;
   				lastSevenDaysCredit += tecCredit;
   				lastSevenDaysCurrency += tecCurrency;
   			} 

   			if (thirtyDaysAgo.isBefore(date) || thirtyDaysAgo.isSame(date)) {
   				lastMonthQty += tecQty;
   				lastMonthCredit += tecCredit;
   				lastMonthCurrency += tecCurrency;
   			}

		});
   		
   		// data for table
		var tblData = [
   			{"period": salesEvoLabels.today, "qty": todayQty, "credit": main.getAmountFormatedValue(todayCredit), "currency": todayCurrency},
   			{"period": salesEvoLabels.yesterday,"qty": yesterdayQty, "credit": main.getAmountFormatedValue(yesterdayCredit), "currency": yesterdayCurrency},
   			{"period": salesEvoLabels.lastSevenDays, "qty": lastSevenDaysQty, "credit": main.getAmountFormatedValue(lastSevenDaysCredit), "currency": lastSevenDaysCurrency},
   			{"period": salesEvoLabels.lastThirtyDays, "qty": lastMonthQty, "credit": main.getAmountFormatedValue(lastMonthCredit), "currency": lastMonthCurrency},
   		];

		// columns for table
   		var columns = [
   			{ "sTitle": "", "mData": "period", "sWidth": "25%" },
     		{ "sTitle": salesEvoLabels.qty, "mData": "qty", "sClass": "rightAlignedColumn", "sWidth": "25%" },
     		{ "sTitle": salesEvoLabels.credit, "mData": "credit", "sClass": "rightAlignedColumn", "sWidth": "25%" },
     		{ "sTitle": salesEvoLabels.currency, "mData": "currency", "sClass": "rightAlignedColumn", "sWidth": "25%" }
   		];

   		// create table
   		$("#tblSalesStats").dataTable({
			"bJQueryUI" : true,
			"bInfo": false,
			"bPaginate": false,
			"bSort": false,
			"bFilter": false,
			"bAutoWidth": false,
			"bDestroy": true,
			"aaData": tblData,
			"aoColumns": columns,
			"fnRowCallback": function( nRow, aData, iDisplayIndex ) {
				$("td", nRow).each(function() {
					var td = $(this);
					var currentTdContents = td.html();
					if(currentTdContents.length > 0 && currentTdContents.indexOf("<span>") != -1) {
						td.html(currentTdContents);
					} else {
						td.html("<span>" + currentTdContents + "</span>");
					}
				});
			}
		});
   		
		/*
		 * now iterate 3 times over the data in order to create intervals of data for quantity, utt and currency
		 * respectively so that we can define interval of rows to show or hide.
		 * For instance the 
		 * quantity rows will be from 0 to records.length, 
		 * utt rows will be from records.length to records.length * 2 - 1
		 * currency rows will be from records.length * 2 to records.length * 3 - 1
		 */
   		// quantity
   		$.each(records, function(i, obj) {
   			
   			var date = new Date(obj.date);
   			var tecQty = obj.tecTotals.totalSalesQuantity;
   			var storeQty = obj.storeTotals.totalSalesQuantity;
   			
   			tecs.salesEvoData.addRow([
              {v: date, f: moment(date).format(formats.daySimple)}, 
              tecQty, 
              storeQty
            ]);
		});
   		// utt
   		$.each(records, function(i, obj) {
   			
   			var date = new Date(obj.date);
   			var tecCredit = obj.tecTotals.totalSalesPoints;
   			var storeCredit = obj.storeTotals.totalSalesPoints;
   			
   			tecs.salesEvoData.addRow([
              {v: date, f: moment(date).format(formats.daySimple)}, 
              {v: main.convertInternalUnitsToExternalUnits(tecCredit), f: main.getAmountFormatedValue(tecCredit)},
              {v: main.convertInternalUnitsToExternalUnits(storeCredit), f: main.getAmountFormatedValue(storeCredit)}
              ]
   			);
   		});
   		// currency
   		$.each(records, function(i, obj) {
   			
   			var date = new Date(obj.date);
   			var tecCurrency = obj.tecTotals.totalSalesCurrency;
   			var storeCurrency = obj.storeTotals.totalSalesCurrency;
   			
   			tecs.salesEvoData.addRow([
              {v: date, f: moment(date).format(formats.daySimple)}, 
              tecCurrency, 
              storeCurrency
            ]);
   		});
   		
   		tecs.salesEvoDataNumRecords = records.length;   		

   		// create chart
   		var chartDiv = document.getElementById("salesEvoChartDiv");
		tecs.chart = new google.visualization.LineChart(chartDiv);
		
		tecs.chartOptions = {
			fontName:"Verdana",
			fontSize: 11,
			hAxis: {format: formats.chartDateOnly},
			vAxis: {viewWindow: {min: 0} },
			animation: {
				duration: 1000,
				easing: 'linear'
	        },
	        width:580,
	        height: 170,
	        chartArea: {width: '70%'}
		};
		
		// create data views of the chart data table
		tecs.salesEvoDataView = new google.visualization.DataView(tecs.salesEvoData);
		tecs.salesEvoDataView.setRows(0, tecs.salesEvoDataNumRecords - 1);
		
		// draw default data (by quantity)
		setTimeout(function() {
			tecs.chart.draw(tecs.salesEvoDataView, tecs.chartOptions);
		}, 10);
	},

	changeChart: function() {
		
		if ($("#chartEvolutionType").val() == 'qty') {
			tecs.salesEvoDataView.setRows(0, tecs.salesEvoDataNumRecords - 1);
		} else if ($("#chartEvolutionType").val() == 'utt') {
			tecs.salesEvoDataView.setRows(tecs.salesEvoDataNumRecords, (tecs.salesEvoDataNumRecords * 2) - 1);
		} else {
			tecs.salesEvoDataView.setRows(tecs.salesEvoDataNumRecords * 2, (tecs.salesEvoDataNumRecords * 3) - 1);
		}
		setTimeout(function() {
			tecs.chart.draw(tecs.salesEvoDataView, tecs.chartOptions);
		}, 10);
	}, 
	
	getEntitiesForTecTransfer : function(selectedOption) {
		$.ajax({
			type : "GET",
			cache : false,
			url : "EntitiesForTecTransfer",
			beforeSend : function() {
				main.showOperationsLoading();
			},
			complete : function() {
				main.hideOperationsLoading();
			},
			success : function(response) {
				if(main.isSessionExpired(response)) {
					return;
				} 
				
				main.destroyInnerListComponents();
				main.destroyInnerComponents();
				$("#tblEntitiesForTecTransfer").html(response);
				
				innerSearch.extraSearchFilters = new Array();
				innerSearch.extraSearchFilters[details.entityIdName] = details.entityIdValue;

				innerList.tableContainerWidth = details.detailsComponent.width();
				
				innerSearch.buildFiltersAndInitializeDataTable();
				
				innerSearch.buildConfigurationMenu();
				
				main.registerInnerListToDestroy("tblEntitiesForTecTransfer");
				details.showNewOptionContent(selectedOption);
			},
			error : function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},

	transferTecToEntity: function(entityId) {
		tecs.targetEntityIdForTecTransfer = entityId;

		// Open confirmation screen
		$("#confirmTransferTec").html(auxTexts.confirmTransferTec
				.replace("[tecId]", details.entityIdValue)
				.replace("[targetEntityId]", tecs.targetEntityIdForTecTransfer));
		
		tecs.openTransferTecDialog();
	},
	
	buildTransferTecComponent: function() {
		$("#transferTec").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent }
		});
		
		$("#transferTec button").button();
		
		main.registerDetailsDialogToDestroy("transferTec");
		
		$("#confirmTransferTecCancelBtn").unbind("click").click(tecs.closeTransferTecDialog);
		$("#confirmTransferTecOkBtn").unbind("click").click(tecs.sendTransferTecRequest);
	},
	
	openTransferTecDialog: function() {
		$("#transferTec").dialog("open");
	},
	
	closeTransferTecDialog: function() {
		$("#transferTec").dialog("close");
	},
	
	sendTransferTecRequest: function() {
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			url : "TransferTec?" + details.entityIdName + "=" + details.entityIdValue
					+ "&targetEntityIdForTecTransfer=" + tecs.targetEntityIdForTecTransfer,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}				
				tecs.closeTransferTecDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					main.showSuccess(response.statusMessage);
					details.getDetailsFromEntity(details.entityIdValue);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},

	buildCreateTecComponent: function() {
		var createDialog = $("#createTec");
		if (createDialog.length == 0) {
			return;
		}
		
		// Create dialog
		$("#createTec").dialog({
			dialogClass : "dialogDetails",
			draggable : false,
			resizable : false,
			width: 690,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+100", of: window },
			open : function(event, ui) {
				$('.ui-widget-overlay').addClass("withOpacity");
				tecs.getCreateTecDataValues();
			},
			close : function(event, ui) {
				$('.ui-widget-overlay').removeClass("withOpacity");
				tecs.getCreateTecDataValues();
			},
			buttons: [{
				text : auxTexts.buttonCancelText,
				click : tecs.cancelSave
			}, {
				text : auxTexts.buttonApplyText,
				click : tecs.configureCreateTecData
			}]
		});
		
		main.registerConfigDialogToDestroy("createTec");
		main.registerElementToDestroy($("#createTec"));
		
		// Apply style to dialog
		main.applyClearableStyles($("#createTec"));
		main.applySelectMenuStyles($("#createTec"));
		
		// Add button
		$("#createTec button").button();
		
		// Add tooltip to button
		search.addExtraInputComponent("createTecBtn", auxTexts.createTec, "button");
		
		// Add style to button
		$("#createTecBtn").button({
			text : false,
			icons : {
				primary : "ui-icon-plusthick"
			}
		});

		// Add click event to button
		$("#createTecBtn").unbind("click").click(function() {
			$("#createTec").dialog("open");
		});
	},
	
	cancelSave: function() {
		$("#createTec").dialog("close");
	},
	
	getCreateTecDataValues: function() {
		// Reset any previously inserted data
		$("#createTecSerial").val("");
		$("#createTecImei").val("");
		$("#createTecImsi").val("");
		$("#createTecIccid").val("");
		$("#createTecMsisdn").val("");
		$("#createTecDeviceId").selectmenu("value", 1);
	},
	
	configureCreateTecData: function() {
		// Clear invalid values warning
		$(".invalidValue").removeClass("invalidValue");
		
		// Parse name
		var serialInput = $("#createTecSerial");
		var serialTd = serialInput.parent().parent();
		var serialRegex = /^[a-zA-Z0-9]{1,18}$/;
		var isSerialValid = main.validateInput(serialInput.val(), serialRegex);
		
		// Parse imei
		var imeiInput = $("#createTecImei");
		var imeiTd = imeiInput.parent().parent();
		var imeiRegex = /^\d{1,15}$/;
		var isImeiValid = main.validateInput(imeiInput.val(), imeiRegex);

		// Parse imsi
		var imsiInput = $("#createTecImsi");
		var imsiTd = imsiInput.parent().parent();
		var imsiRegex = /^\d{1,15}$/;
		var isImsiValid = main.validateInput(imsiInput.val(), imsiRegex);
		
		// Parse iccid
		var iccidInput = $("#createTecIccid");
		var iccidTd = iccidInput.parent().parent();
		var iccidRegex = /^\w{1,20}$/;
		var isIccidValid = main.validateInput(iccidInput.val(), iccidRegex);
		
		// Parse msisdn
		var msisdnInput = $("#createTecMsisdn");
		var msisdnTd = msisdnInput.parent().parent();
		var msisdnRegex = /^\d{1,14}$/;
		var isMsisdnValid = main.validateInput(msisdnInput.val(), msisdnRegex);
		
		// Invalid values?
		if (!isSerialValid || !isImeiValid || !isImsiValid || !isIccidValid || !isMsisdnValid) {
			
			// Invalid serial?
			if(!isSerialValid) {
				serialTd.addClass("invalidValue");
				serialInput.parent().addClass("invalidValue");
				serialInput.addClass("invalidValue");
			}
			
			// Invalid imei?
			if(!isImeiValid) {
				imeiTd.addClass("invalidValue");
				imeiInput.parent().addClass("invalidValue");
				imeiInput.addClass("invalidValue");
			}
			
			// Invalid imsi?
			if(!isImsiValid) {
				imsiTd.addClass("invalidValue");
				imsiInput.parent().addClass("invalidValue");
				imsiInput.addClass("invalidValue");
			}
			
			// Invalid iccid?
			if(!isIccidValid) {
				iccidTd.addClass("invalidValue");
				iccidInput.parent().addClass("invalidValue");
				iccidInput.addClass("invalidValue");
			}
			
			// Invalid msisdn?
			if(!isMsisdnValid) {
				msisdnTd.addClass("invalidValue");
				msisdnInput.parent().addClass("invalidValue");
				msisdnInput.addClass("invalidValue");
			}
			
			main.showWarning(auxTexts.errorValidatePropertyText);
			return;
		}
		
		// Send request
		tecs.sendCreateTecRequest();
	},
	
	sendCreateTecRequest: function() {
		var dataToSend = $("#createTecForm").serialize();
		
		$.ajax({
			type: "POST",
			cache: false,
			url: "CreateTec",
			data: dataToSend,
			dataType: "json",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}				
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					search.applyConfigurations();
					main.showSuccess(response.statusMessage);
					$("#createTec").dialog("close");
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildGenerateTecAdminPinComponent: function() {
		$("#generateTecAdminPin").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#generateTecAdminPin button").button();
		
		main.registerDetailsDialogToDestroy("generateTecAdminPin");
		
		$("#generateTecAdminPinCancelBtn").unbind("click").click(tecs.closeGenerateTecAdminPinDialog);
		$("#generateTecAdminPinOkBtn").unbind("click").click(tecs.generateTecAdminPin);
		$("#generatedTecAdminTokenInput").keypress(function (event) {
			if (event.which == 13) {
				tecs.generateTecAdminPin();
			}
		});
	},
	
	openGenerateTecAdminPinDialog: function() {
		$("#generatedTecAdminTokenInput").val("");
		$("#generateTecAdminPin").dialog("open");
		main.focusLater($("#generatedTecAdminTokenInput"));
	},
	
	closeGenerateTecAdminPinDialog: function() {
		$("#generatedTecAdminTokenInput").val("");
		$("#generateTecAdminPin").dialog("close");
	},
	
	generateTecAdminPin: function() {
		var iccid = details.entityData.reply.tec.iccid;
		var imei = details.entityData.reply.tec.imei;
		var token = $("#generatedTecAdminTokenInput").val();

		// Invalid token?
		if (token.length != 4 || !tecs.isNumber(token)) {
			main.showWarning(tokenText.missingOrInvalidToken);
			return;
		}
		
		// Parse token
		var pos1 = parseInt(token[0]);
		var pos2 = parseInt(token[1]);
		var pos3 = parseInt(token[2]);
		var pos4 = parseInt(token[3]);

		// Invalid tec data?
		if (imei.length < pos2 || imei.length < 10 - pos1 || iccid.length < pos4 || iccid.length < 10 - pos3) {
			main.showWarning(tokenText.missingOrInvalidTecData);
			return;
		}

		// Compute PIN
		validPin = imei[pos2] + "" + iccid[pos4] + "" + iccid[10 - pos3] + "" + imei[10 - pos1];
		
		// Show generated PIN
		$("#generatedTecAdminPin").html(tokenText.generatedTecAdminPin + ": " + validPin);
		tecs.closeGenerateTecAdminPinDialog();
		tecs.openShowGeneratedTecAdminPinDialog();
	},
	
	isNumber : function(str) {
		for ( var i = 0, len = str.length; i < len; i++) {
			if ("**********".indexOf(str[i]) < 0) {
				return false;
			}
		}
		return true;
	},
	
	buildShowGeneratedTecAdminPinComponent: function() {
		$("#showGeneratedTecAdminPin").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#showGeneratedTecAdminPin button").button();
		
		main.registerDetailsDialogToDestroy("showGeneratedTecAdminPin");
		
		$("#generatedTecAdminPinOkBtn").unbind("click").click(tecs.closeShowGeneratedTecAdminPinDialog);
	},
	
	openShowGeneratedTecAdminPinDialog: function() {
		$("#showGeneratedTecAdminPin").dialog("open");
	},
	
	closeShowGeneratedTecAdminPinDialog: function() {
		$("#generatedTecAdmin").val("");
		$("#showGeneratedTecAdminPin").dialog("close");
	},

	loadSalesLimitData : function(selectedOption) {
		// Get current data
		var tecData = details.entityData.reply.tec;
		
		$("#editSalesLimitEntityId").val(tecData.tecId);
		
		$("#editSalesLimit").val(main.getAmountFormatedValue(tecData.salesLimit, false));

		if (tecData.blockOnSalesLimit) {
			$("#blockOnSalesLimit_active").prop("checked", true).button("refresh");
		} else {
			$("#blockOnSalesLimit_inactive").prop("checked", true).button("refresh");
		}
		
		// Sales limit is active?
		if (tecData.salesLimit != null) {
			$("#salesLimitStatus_active").attr("checked", true).button("refresh");
			$("#editBlockOnSalesLimit").parent().parent().show();
			$("#editSalesLimit").parent().parent().parent().show();
			
		} else {
			$("#salesLimitStatus_inactive").attr("checked", true).button("refresh");
			$("#editBlockOnSalesLimit").parent().parent().hide();
			$("#editSalesLimit").parent().parent().parent().hide();
		}
	
		// Set click events when sales limit is not active
		$("#salesLimitStatus_active").click(function() {
			$("#editBlockOnSalesLimit").parent().parent().show();
			$("#editSalesLimit").parent().parent().parent().show();
			
			// Resize details container
			details.detailsContentsRealHeight = $(".detailsBodyContent:visible table.detailsContainer").height();
			details.resizeInnerContents();
		});
		$("#salesLimitStatus_inactive").click(function() {
			$("#editBlockOnSalesLimit").parent().parent().hide();
			$("#editSalesLimit").parent().parent().parent().hide();
			
			// Resize details container
			details.detailsContentsRealHeight = $(".detailsBodyContent:visible table.detailsContainer").height();
			details.resizeInnerContents();
		});
		
		details.showNewOptionContent(selectedOption);
	},
	
	loadUpdateIccidData : function(selectedOption) {
		// Get current data
		var tecData = details.entityData.reply.tec;
		
		$("#updateIccidEntityId").val(tecData.tecId);
		
		$("#updateIccidId").val(tecData.tecId);
		$("#updateIccidSerial").val(tecData.serial);
		$("#updateIccidImei").val(tecData.imei);
		$("#updateIccidImsi").val(tecData.imsi);
		$("#updateIccidIccid").val(tecData.iccid);
		$("#updateIccidMsisdn").val(tecData.msisdn);
		
		$("#updateIccidId").css("opacity", "0.8");
		$("#updateIccidSerial").css("opacity", "0.8");
		$("#updateIccidImei").css("opacity", "0.8");
		$("#updateIccidImsi").css("opacity", "0.8");
		$("#updateIccidMsisdn").css("opacity", "0.8");

		
		details.showNewOptionContent(selectedOption);
	},
	
	loadTerminalReplaceData : function(selectedOption) {
		// Get current data
		var tecData = details.entityData.reply.tec;
		
		$("#terminalReplaceEntityId").val(tecData.tecId);
		
		$("#terminalReplaceId").val(tecData.tecId);
		$("#terminalReplaceSerial").val(tecData.serial);
		$("#terminalReplaceImei").val(tecData.imei);
		$("#terminalReplaceImsi").val(tecData.imsi);
		$("#terminalReplaceIccid").val(tecData.iccid);
		$("#terminalReplaceMsisdn").val(tecData.msisdn);
		
		$("#terminalReplaceId").css("opacity", "0.8");
		$("#terminalReplaceIccid").css("opacity", "0.8");
		$("#terminalReplaceImsi").css("opacity", "0.8");
		$("#terminalReplaceMsisdn").css("opacity", "0.8");

		
		details.showNewOptionContent(selectedOption);
	},
	
	loadSimReplaceData : function(selectedOption) {
		// Get current data
		var tecData = details.entityData.reply.tec;
		
		$("#simReplaceEntityId").val(tecData.tecId);
		
		$("#simReplaceId").val(tecData.tecId);
		$("#simReplaceSerial").val(tecData.serial);
		$("#simReplaceImei").val(tecData.imei);
		$("#simReplaceImsi").val(tecData.imsi);
		$("#simReplaceIccid").val(tecData.iccid);
		$("#simReplaceMsisdn").val(tecData.msisdn);
		
		$("#simReplaceId").css("opacity", "0.8");
		$("#simReplaceImei").css("opacity", "0.8");
		$("#simReplaceSerial").css("opacity", "0.8");
		
		details.showNewOptionContent(selectedOption);
	},
	
	// build query string from the list filters
	getQueryString: function() {
		var tecType = $("input[name='tecType']").val();
		return search.currentSearchSource.substring(search.currentSearchSource.indexOf("?")) + "&tecType=" + tecType;
	},
	
	buildConfirmGeofencingRemove: function() {
		$("#confirmGeofencingRemove").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
		});
		
		$("#confirmGeofencingRemove button").button();
		
		main.registerDetailsDialogToDestroy("confirmGeofencingRemove");
		
		$("#confirmGeofencingRemoveCancelBtn").unbind("click").click(tecs.closeConfirmGeofencingRemoveDialog);
		$("#confirmGeofencingRemoveOkBtn").unbind("click").click(tecs.removeTecGeofencing);
	},
	
	buildConfirmGeofencingEdit: function() {
		$("#confirmGeofencingEdit").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
		});
		
		$("#confirmGeofencingEdit button").button();
		
		main.registerDetailsDialogToDestroy("confirmGeofencingEdit");
		
		$("#confirmGeofencingEditCancelBtn").unbind("click").click(tecs.closeConfirmGeofencingEditDialog);
		$("#confirmGeofencingEditOkBtn").unbind("click").click(tecs.setTecGeofencing);
	},
	
	getTecGeofencingInfo: function() {
		$.ajax({
			type: "GET",
			cache: false,
			url: "TecGeofencing?tecId=" + details.entityIdValue,
			dataType: "json",
			beforeSend: function() {
//				main.showOperationsLoading();
			},
			complete: function() {
//				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.tecGeofencedSites = response.tecGeofencedSites;
					tecs.populateRegionsTree();
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	validateGeofencingSitesLimit: function() {
		if(geofencingProperties.maxSitesSelection > 0 && geofencingMaps.selectedSites.length > geofencingProperties.maxSitesSelection) {
			main.showWarning(auxTexts.sitesLimitExceeded);
			return;
		}
		
		tecs.openConfirmGeofencingEditDialog();
	},
	
	openConfirmGeofencingEditDialog: function() {
		$("#confirmGeofencingEdit").dialog("open");
	},
	
	closeConfirmGeofencingEditDialog: function() {
		$("#confirmGeofencingEdit").dialog("close");
	},
	
	setTecGeofencing: function() {
		
		if(geofencingMaps.selectedSites.length == 0) {
			main.showWarning(auxTexts.warningChooseSites);
			return;
		}
		
		var applyToStore = $("#editAllStoreTerminals").is(":checked");
		var applyToAgent = $("#editAllAgentTerminals").is(":checked");
		var allowUnknown = $("#allowSalesUnknownCells").is(":checked");
		
		var operation = "SetTecGeofencing?tecId=" + details.entityIdValue;
		
		if(applyToAgent) {
			operation = "SetAgentTecsGeofencing?agentId=" + details.entityData.reply.tec.agentId;
		} else if(applyToStore) {
			operation = "SetStoreTecsGeofencing?storeId=" + details.entityData.reply.tec.storeId;
		}
		
		$.ajax({
			type: "POST",
			cache: false,
			url: operation,
			data: "selectedSites=" + geofencingMaps.selectedSites.join() + "&sellOnUnknownRegions="+ allowUnknown,
			dataType: "json",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.closeConfirmGeofencingEditDialog();
					details.entityData.reply.tec.geofencingEnabled = true;
					tecs.validateGeofencingInfo();
					main.showSuccess(response.statusMessage);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildGeoLimitContainer: function() {
		tecs.geoLimitContainer = $("#salesTecsGeoLimit");

		if(tecs.geoLimitContainer.length > 0) {
			tecs.geoLimitContainer.dialog({
				dialogClass : "dialogDetails",
				draggable : false,
				resizable : false,
				width: 800,
				minHeight : 500,
				modal : true,
				autoOpen : false,
				open : function(event, ui) {
					$('.ui-widget-overlay').click(function() {
						tecs.geoLimitContainer.dialog("close");
					});
					$('.ui-widget-overlay').addClass("withOpacity");
				},
				close : function(event, ui) {
					$('.ui-widget-overlay').removeClass("withOpacity");
				}
			});
			
			main.registerConfigDialogToDestroy("salesTecsGeoLimit");
			main.registerElementToDestroy(tecs.geoLimitContainer);
			
			search.addExtraInputComponent("geoLimitsTecsBtn", auxTexts.geoLimitTecs, "button");
			
			$("#geoLimitsTecsBtn").button({
				text : false,
				icons : {
					primary : "ui-icon-pin-s"
				}
			});
			
			$("#geoLimitsTecsBtn").unbind("click").click(tecs.showGeoLimitDialog);
			
		}
	},
	
	showGeoLimitDialog: function() {
		tecs.geoLimitContainer.dialog("open");
		tecs.getTecsGeoLimits();
	},
	
	getTecsGeoLimits: function() {
		$.ajax({
			type: "GET",
			cache: false,
			url: "TecsGeofencing"+ tecs.getQueryString(),
			dataType: "json",
			beforeSend: function() {
//				main.showOperationsLoading();
			},
			complete: function() {
//				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.parseTecsGeoLimits(response.tecListGeoInfo);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	openConfirmGeofencingRemoveDialog: function() {
		$("#confirmGeofencingRemove").dialog("open");
	},
	
	closeConfirmGeofencingRemoveDialog: function() {
		$("#confirmGeofencingRemove").dialog("close");
	},
	
	removeTecGeofencing: function() {
		var applyToStore = $("#removeFromAllStoreTerminals").is(":checked");
		var applyToAgent = $("#removeFromAllAgentTerminals").is(":checked");
		
		var operation = "RemoveTecGeofencing?tecId=" + details.entityIdValue;
		
		if(applyToAgent) {
			operation = "RemoveAgentTecsGeofencing?agentId=" + details.entityData.reply.tec.agentId;
		} else if(applyToStore) {
			operation = "RemoveStoreTecsGeofencing?storeId=" + details.entityData.reply.tec.storeId;
		}
		
		$.ajax({
			type: "POST",
			cache: false,
			url: operation,
			dataType: "json",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.closeConfirmGeofencingRemoveDialog();
					details.reloadDetailsDialog();
					main.showSuccess(response.statusMessage);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	parseRegionsTree: function(elements) {

		$("#geofencingRegionsTree").html("");
		
		tecs.level2Elements = new Array();
		tecs.level3Elements = new Array();
		tecs.level4Elements = new Array();
		
		//main.testAntennas = new Array();
		
		var elem;
		for(var i=0; i<elements.length; i++) {
			elem = elements[i];
			
			if(elem.regionLevel == 2) {
				tecs.level2Elements.push(elem);
			} else if(elem.regionLevel == 3) {
				tecs.level3Elements.push(elem);
				
				var sites = elem.sites;
				for(var j=0; j<sites.length; j++) {
					tecs.level4Elements.push(sites[j]);
					//main.testAntennas.push(sites[j]);
				}
			}
		}
		
		tecs.drawGeofencingMapElements();
		tecs.buildLevel2GeofencingTreeElements();
	},
	
	parseTecsGeoLimits: function(elements) {

		tecs.tecsGeoLimits = elements;

		tecs.drawTecsGeoLimitsMapElements();
	},
		
	drawTecsGeoLimitsMapElements: function() {
		
		maps.buildInitialElements(tecs.tecsGeoLimits);
		
	},
	
	drawGeofencingMapElements: function() {
		geofencingMaps.buildInitialElements(details.entityData.reply.tec.tecStats.locationLatitude, details.entityData.reply.tec.tecStats.locationLongitude, tecs.level4Elements, tecs.tecGeofencedSites);
		details.detailsContentsRealHeight = $(".detailsBodyContent:visible table.innerMapContainer").height();
		details.resizeContents();
	},
	
	buildLevel2GeofencingTreeElements: function() {
		var interval = setInterval(function() {
			if(tecs.level2Elements.length > 0) {
				// Pop a level 2 node
				var node = tecs.level2Elements.pop();

				// Get level 3 children
				var children = tecs.getChildrenRegionsFromId(node.regionId);

				// Draw level 2 node
				var parentRow = tecs.drawLevel2GeofencingTreeElement(node);

				// Draw level 3 children
				tecs.buildLevel3GeofencingTreeElements(parentRow, children);

				} else {
					main.stopInterval(interval);
				}
		}, 0);
		
		main.registerIntervalToStop(interval);
	},

	getChildrenRegionsFromId : function(regionId) {
		var children = new Array();
		
		// Iterate over all level 3 elements
		for (var i = 0; i < tecs.level3Elements.length; i++) {
			
			// Found a child?
			if (regionId == tecs.level3Elements[i].parentRegionId) {
				
				// Add to children array
				children.push(tecs.level3Elements[i]);
			}
		}

		return children;
	},
	
	buildLevel3GeofencingTreeElements: function(parentRow, level3Nodes) {
		var newRow = parentRow;
		var interval = setInterval(function() {
			if(level3Nodes.length > 0) {
				var node = level3Nodes.pop();
				newRow = tecs.drawLevel3GeofencingTreeElement(parentRow, newRow, node);

			} else {
				main.stopInterval(interval);
			}
		}, 0);
		
		main.registerIntervalToStop(interval);
	},
	
	selectAllChildrenNodesFromNode: function(parenNode, state) {
		var parentNodeId = parenNode.attr("id");
		var parentNodeChildren = $("[parent='"+ parentNodeId + "']");
		
		parentNodeChildren.each(function() {
			var thisNode = $(this);
			var originalState = thisNode.find("input").is(":checked");
			if(originalState != state) {
				thisNode.find("input").click();
			}
		});
		return;
	},
	
	drawLevel2GeofencingTreeElement: function(node) {
		
		var nodeTemplateHtml = '<tr id="' + node.regionId + '" type="level2"><td><span class="treegrid-expander treegrid-expander-collapsed"></span><span class="regionsTreeElementName" style="width:195px"><input type="checkbox"/>&nbsp;' + node.regionName + '</span></td></tr>';
		var row = $(nodeTemplateHtml);
		
		row.unbind("click").click(function(){
			geofencingMaps.zoomToPoint(node.pinpointLatitude, node.pinpointLongitude, 8);
		});
		
		var checkbox = row.find("input");
		
		checkbox.unbind("click").click(function() {
			tecs.selectAllChildrenNodesFromNode(checkbox.closest("tr"), checkbox.is(':checked'));
			return true;
		});
		
		var expander = row.find(".treegrid-expander");

		expander.click(function() {
			var isCollapsed = expander.hasClass("treegrid-expander-collapsed");

			if(isCollapsed) {
				expander.removeClass("treegrid-expander-collapsed").addClass("treegrid-expander-expanded");
				tecs.showChildrenNodes(row);
			} else {
				expander.removeClass("treegrid-expander-expanded").addClass("treegrid-expander-collapsed");
				tecs.hideChildrenNodes(row);
			}
			return false;
		});
		
		$("#geofencingRegionsTree").append(row);
		
		return row;
	},
	
	drawLevel3GeofencingTreeElement: function(parentRow, prevRow, node) {
		var indentLevelTemplate = '<span class="treegrid-indent"></span>';

		var parentRowIndentLevel = parentRow.find(".treegrid-indent").length;
		
		var subRowWidth = 195 - ((parentRowIndentLevel + 1) * 30);

		var indentHtml = "";
		for(var i=0; i< parentRowIndentLevel + 1 ; i++) {
			indentHtml += indentLevelTemplate;
		}

		var nodeTemplateHtml = '<tr id="' + node.regionId + '" type="level3" parent="' + node.parentRegionId + '" ><td>' + indentHtml + '<span class="treegrid-expander treegrid-expander-collapsed"></span><span class="regionsTreeElementName" style="width:' + subRowWidth + 'px"><input type="checkbox"/>&nbsp;' + node.regionName + '</span></td></tr>';

		var row = $(nodeTemplateHtml);
		row.hide();
		
		row.unbind("click").click(function(){
			geofencingMaps.zoomToPoint(node.pinpointLatitude, node.pinpointLongitude, 10);
		});
		
		var checkbox = row.find("input");
		
		checkbox.unbind("click").click(function() {
			tecs.selectAllChildrenNodesFromNode(checkbox.closest("tr"), checkbox.is(':checked'));
			tecs.updateHierarchyFromChild(row);
			return true;
		});
		
		var expander = row.find(".treegrid-expander");
		
		expander.unbind("click").click(function() {
			var isCollapsed = expander.hasClass("treegrid-expander-collapsed");

			if(isCollapsed) {
				expander.removeClass("treegrid-expander-collapsed").addClass("treegrid-expander-expanded");
				tecs.showChildrenNodes(row);
			} else {
				expander.removeClass("treegrid-expander-expanded").addClass("treegrid-expander-collapsed");
				tecs.hideChildrenNodes(row);
			}
			return false;
		});
		
		prevRow.after(row);
		
		tecs.drawLevel4GeofencingTreeElements(row, node.sites);
		
		return row;
		
	},
	
	drawLevel4GeofencingTreeElements: function(parentRow, children) {
		var indentLevelTemplate = '<span class="treegrid-indent"></span>';

		var parentRowIndentLevel = parentRow.find(".treegrid-indent").length;
		
		var subRowWidth = 195 - ((parentRowIndentLevel + 1) * 30);

		var indentHtml = "";
		for(var i=0; i< parentRowIndentLevel + 1 ; i++) {
			indentHtml += indentLevelTemplate;
		}
		
		var prevRow = parentRow;
		
		var interval = setInterval(function() {
			if(children.length > 0) {
				var node = children.pop();
				
				var nodeTemplateHtml = '<tr id="' + node.code + '" type="level4" parent="' + node.subRegionId + '" ><td>' + indentHtml + '<span class="regionsTreeElementName" style="width:' + subRowWidth + 'px"><input type="checkbox"/>&nbsp;' + node.code + '</span></td></tr>';
				
				var row = $(nodeTemplateHtml);
				row.hide();
				
				row.unbind("click").click(function(){
					geofencingMaps.zoomToPoint(node.latitude, node.longitude, 16);
				});
				
				prevRow.after(row);
				
				prevRow = row;
				
				var checkbox = row.find("input");
				
				checkbox.unbind("click");
				
				if($.inArray(node.code, tecs.tecGeofencedSites) > -1) {
					checkbox.click();
					tecs.updateParentNodeFromChild(row, true);
				}
				
				checkbox.click(function() {
					
					var newStatus = checkbox.is(":checked");
					if(newStatus == true) {
						if(geofencingMaps.selectedSites.length == 0) {
							$(".ui-dialog-buttonpane button:contains('"+ auxTexts.buttonApplyGeofencingText +"')").button("enable");
						}
						geofencingMaps.addToSelectedSites(node.code);
					} else{
						geofencingMaps.removeFromSelectedSites(node.code);

						if(geofencingMaps.selectedSites.length == 0) {
							$(".ui-dialog-buttonpane button:contains('"+ auxTexts.buttonApplyGeofencingText +"')").button("disable");
						}
					}
					
					tecs.updateHierarchyFromChild(row);
					
					return true;
				});
				tecs.sitesCounter++;
				
			} else {
				clearInterval(interval);
				if(tecs.sitesCounter == tecs.level4Elements.length) {
					$("#geofencingRegionsTree input").removeAttr("disabled");
					$("#selectVisibleBtn").button("enable");
					tecs.sitesCounter = 0;
					main.hideOperationsLoading();
				}
			}
		}, 0);
		
		main.registerIntervalToStop(interval);
	},
	
	
	openParentNodeFromChild: function(row) {
		var parentId = row.attr("parent");
		var rowType = row.attr("type");
		
		// Already reached top level?
		if (parentId == null || rowType == "level2") {
			return;
		}
		
		// Get parent row
		var parentRow = $("#geofencingRegionsTree #" + parentId);
		
		// Get expander element
		var expander = parentRow.find(".treegrid-expander");

		// Check if element is collapsed
		if (expander.hasClass("treegrid-expander-collapsed")) {
			expander.removeClass("treegrid-expander-collapsed").addClass("treegrid-expander-expanded");
		}
		
		// Expand children nodes
		tecs.showChildrenNodes(parentRow);
		tecs.openParentNodeFromChild(parentRow);
	},

	updateParentNodeFromChild: function(row, state) {
		var parentId = row.attr("parent");
		var rowType = row.attr("type");

		// Crawl the tree to top level
		while (parentId != null && rowType != "level2") {

			// Get parent row
			var parentRow = $("#geofencingRegionsTree #" + parentId);

			// Parse next parent
			parentId = parentRow.attr("parent");
			rowType = parentRow.attr("type");

			var checkbox = parentRow.find("input");
			checkbox.prop("checked", state);
		}
	},
	
	updateHierarchyFromChild: function(row) {
		var parentId = row.attr("parent");
		var rowType = row.attr("type");

		// Crawl the tree to top level
		while (parentId != null && rowType != "level2") {

			// Get brothers
			var brothers = $("[parent='"+ parentId + "']");

			// Check if any brother is checked
			var isAnyChecked = false;
			brothers.each(function() {
				var thisNode = $(this);

				var checkbox = thisNode.find("input");
				if (checkbox.is(':checked')) {
					isAnyChecked = true;
					return false;
				}
			});

			// Get parentRow
			var parentRow = $("#geofencingRegionsTree #" + parentId);

			// Get parent checkbox
			var checkbox = parentRow.find("input");

			// Set checkbox state accordingly
			checkbox.prop("checked", isAnyChecked);

			// Parse next parent
			parentId = parentRow.attr("parent");
			rowType = parentRow.attr("type");
		}
	},

	hideChildrenNodes: function(parenNode) {
		var parentNodeId = parenNode.attr("id");
		var parentNodeChildren = $("[parent='"+ parentNodeId + "']");
		
		parentNodeChildren.each(function() {
			var thisNode = $(this);
			tecs.hideChildrenNodes(thisNode);
			thisNode.hide();
		});
	},
	
	showChildrenNodes: function(parenNode) {
		var parentNodeId = parenNode.attr("id");
		var parentNodeChildren = $("[parent='"+ parentNodeId + "']");
		
		parentNodeChildren.each(function() {
			var thisNode = $(this);
			thisNode.show();
		});
	},
	
	updateRegionsTreeSingleMarker : function(marker) {
		var markerState = marker.enabled;
		var siteCode = marker.code;
		
		var siteRow = $("#geofencingRegionsTree #" + siteCode);
		var input = siteRow.find("input");
		var initialState = input.is(":checked");
		
		if(initialState != markerState) {
			
			input.click();
			tecs.openParentNodeFromChild(siteRow);
		}
	},
	
	validateGeofencingInfo: function() {
		$("#selectVisibleBtn").button("disable");
		
		main.showOperationsLoading();

		// Remove any previous extraDetailsComponent
		$("#geofencingInfoTd").closest( 'tr' ).remove();
		
		// Add label to the top of the details form
		var extraDetailsHtml = '<tr><td colspan="2" id="geofencingInfoTd"></td></tr>';
		var extraDetails = $(extraDetailsHtml);
		$("#body_tecsetgeofencing table tr:first-of-type").before(extraDetails);
		
		var geofencingEnabled = details.entityData.reply.tec.geofencingEnabled;
		if (geofencingEnabled == false) {
			$("#geofencingInfoTd").addClass("noGeofencingWarning");
			$("#geofencingInfoTd").html(auxTexts.geofencingDisabled);
			
			$(".ui-dialog-buttonpane button:contains('"+ auxTexts.buttonRemoveGeofencingText +"')").button("disable");
			$(".ui-dialog-buttonpane button:contains('"+ auxTexts.buttonApplyGeofencingText +"')").button("disable");
			
		} else {
			$(".ui-dialog-buttonpane button:contains('"+ auxTexts.buttonRemoveGeofencingText +"')").button("enable");
			$(".ui-dialog-buttonpane button:contains('"+ auxTexts.buttonApplyGeofencingText +"')").button("enable");
			
		}
		tecs.getTecGeofencingInfo();
	},
	
	populateRegionsTree: function() {
		$.ajax({
			type: "GET",
			cache: false,
			url: "PopulateRegionsTree",
			dataType: "json",
			beforeSend: function() {
//				main.showOperationsLoading();
			},
			complete: function() {
//				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.parseRegionsTree(response.regionsTree.reverse());
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
		
		
	},
	
	selectGeofencingVisibleArea: function() {
		geofencingMaps.updateSelectedSitesWithBounds();
	},
	
	configureSalesLimitData : function() {
		
		// Clear invalid values warning
		$(".invalidValue").removeClass("invalidValue");
		
		// Get sales limit status
		var salesLimitStatus = $('input[name="salesLimitStatus"]:checked').val();
		
		// Parse sales limit
		var salesLimitInput = $("#editSalesLimit");
		var salesLimitTd = salesLimitInput.parent().parent();
		var salesLimitRegex = regexs.creditUnsigned;
		var salesLimitValue = salesLimitInput.val();
		var salesLimitNoSpaces = main.getUnformatedValue(salesLimitValue);
		var isSalesLimitValid = main.validateInput(salesLimitNoSpaces, salesLimitRegex);
		
		// Invalid value?
		if (salesLimitStatus === 'true' && isSalesLimitValid == false) {
			salesLimitTd.addClass("invalidValue");
			salesLimitInput.parent().addClass("invalidValue");
			salesLimitInput.addClass("invalidValue");
	
			main.showWarning(auxTexts.errorValidatePropertyText);
			return;
		}
	
		// Transform external units to internal units
		if (salesLimitStatus === 'false') {
			$("input[name='salesLimit']").val(0);
		} else {
			$("input[name='salesLimit']").val(main.convertExternalUnitsToInternalUnits(parseFloat(salesLimitNoSpaces)));
		}
		
		// Send request
		var editForm = $('#editSalesLimitForm');
		var dataToSend = editForm.serialize();
		
		$.ajax({
			type: "POST",
			url: "TecEditSalesLimit",
			data: dataToSend,
			dataType: "json",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				if(!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					search.applyConfigurations();
					main.showSuccess(response.statusMessage);
					details.getDetailsFromEntity(details.entityIdValue);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	openConfirmUpdateIccidDialog: function() {
		$("#confirmUpdateIccid").dialog("open");
	},
	
	closeConfirmUpdateIccidDialog: function() {
		$("#confirmUpdateIccid").dialog("close");
	},
	
	validateUpdateIccidData : function() {
		
		// Clear invalid values warning
		$(".invalidValue").removeClass("invalidValue");
		
		// Parse iccid
		var iccidInput = $("#updateIccidIccid");
		var iccidTd = iccidInput.parent().parent();
		var iccidRegex = /^\w{1,20}$/;
		var isIccidValid = main.validateInput(iccidInput.val(), iccidRegex);
		
		// Invalid iccid?
		if(!isIccidValid) {
			iccidTd.addClass("invalidValue");
			iccidInput.parent().addClass("invalidValue");
			iccidInput.addClass("invalidValue");
			
			main.showWarning(auxTexts.errorValidatePropertyText);
			return;
		}
		
		tecs.openConfirmUpdateIccidDialog();
		
	},
	
	confirmUpdateIccidData : function() {

		// Send request
		var editForm = $('#updateIccidEditForm');
		
		var idInput = editForm.find('tr:first-of-type input').removeAttr('disabled');
		var dataToSend = editForm.serialize();
		idInput.attr('disabled','disabled');
		
		$.ajax({
			type: "POST",
			url: "TecUpdateIccid",
			data: dataToSend,
			dataType: "json",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
								
				if(!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.closeConfirmUpdateIccidDialog();
					search.applyConfigurations();
					main.showSuccess(response.statusMessage);
					details.getDetailsFromEntity(details.entityIdValue);
				}
				
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	openConfirmTerminalReplaceDialog: function() {
		$("#confirmTerminalReplace").dialog("open");
	},
	
	closeConfirmTerminalReplaceDialog: function() {
		$("#confirmTerminalReplace").dialog("close");
	},
	
	
	validateTerminalReplaceData: function() {
		// Clear invalid values warning
		$(".invalidValue").removeClass("invalidValue");
		
		// Parse imei
		var imeiInput = $("#terminalReplaceImei");
		var imeiTd = imeiInput.parent().parent();
		var imeiRegex = /^\d{1,15}$/;
		var isImeiValid = main.validateInput(imeiInput.val(), imeiRegex);
		
		// Parse name
		var serialInput = $("#terminalReplaceSerial");
		var serialTd = serialInput.parent().parent();
		var serialRegex = /^[a-zA-Z0-9]{1,18}$/;
		var isSerialValid = main.validateInput(serialInput.val(), serialRegex);
		
		var isValidData = isImeiValid && isSerialValid;
		
		// Invalid value?
		if (!isValidData) {
			// Invalid iccid?
			if(!isImeiValid) {
				imeiTd.addClass("invalidValue");
				imeiInput.parent().addClass("invalidValue");
				imeiInput.addClass("invalidValue");
			}
			
			// Invalid serial?
			if(!isSerialValid) {
				serialTd.addClass("invalidValue");
				serialInput.parent().addClass("invalidValue");
				serialInput.addClass("invalidValue");
			}
			
			main.showWarning(auxTexts.errorValidatePropertyText);
			return;
		}
		
		tecs.openConfirmTerminalReplaceDialog();
		
	},
	
	confirmTerminalReplaceData : function() {
		
		// Send request
		var editForm = $('#terminalReplaceEditForm');
		
		var idInput = editForm.find('tr:first-of-type input').removeAttr('disabled');
		var dataToSend = editForm.serialize();
		idInput.attr('disabled','disabled');

		$.ajax({
			type: "POST",
			url: "TecTerminalReplace",
			data: dataToSend,
			dataType: "json",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
							
				if(!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.closeConfirmTerminalReplaceDialog();
					search.applyConfigurations();
					main.showSuccess(response.statusMessage);
					details.getDetailsFromEntity(details.entityIdValue);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	openConfirmSimReplaceDialog: function() {
		$("#confirmSimReplace").dialog("open");
	},
	
	closeConfirmSimReplaceDialog: function() {
		$("#confirmSimReplace").dialog("close");
	},
	
	
	validateSimReplaceData: function() {
		// Clear invalid values warning
		$(".invalidValue").removeClass("invalidValue");
		
		// Parse iccid
		var iccidInput = $("#simReplaceIccid");
		var iccidTd = iccidInput.parent().parent();
		var iccidRegex = /^\w{1,20}$/;
		var isIccidValid = main.validateInput(iccidInput.val(), iccidRegex);
		
		// Parse imsi
		var imsiInput = $("#simReplaceImsi");
		var imsiTd = imsiInput.parent().parent();
		var imsiRegex = /^\d{1,15}$/;
		var isImsiValid = main.validateInput(imsiInput.val(), imsiRegex);
		
		// Parse msisdn
		var msisdnInput = $("#simReplaceMsisdn");
		var msisdnTd = msisdnInput.parent().parent();
		var msisdnRegex = /^\d{1,14}$/;
		var isMsisdnValid = main.validateInput(msisdnInput.val(), msisdnRegex);
		
		var isValidData = isIccidValid && isImsiValid && isMsisdnValid;
		
		// Invalid value?
		if (!isValidData) {
			// Invalid iccid?
			if(!isIccidValid) {
				iccidTd.addClass("invalidValue");
				iccidInput.parent().addClass("invalidValue");
				iccidInput.addClass("invalidValue");
				
			}
			
			// Invalid imsi?
			if(!isImsiValid) {
				imsiTd.addClass("invalidValue");
				imsiInput.parent().addClass("invalidValue");
				imsiInput.addClass("invalidValue");
			}
						
			// Invalid msisdn?
			if(!isMsisdnValid) {
				msisdnTd.addClass("invalidValue");
				msisdnInput.parent().addClass("invalidValue");
				msisdnInput.addClass("invalidValue");
			}
			
			main.showWarning(auxTexts.errorValidatePropertyText);
			return;
		}
		
		tecs.openConfirmSimReplaceDialog();
	},
	
	confirmSimReplace : function() {
		
		// Send request
		var editForm = $('#simReplaceEditForm');
		
		var idInput = editForm.find('tr:first-of-type input').removeAttr('disabled');
		var dataToSend = editForm.serialize();
		idInput.attr('disabled','disabled');
		
		$.ajax({
			type: "POST",
			url: "TecSimReplace",
			data: dataToSend,
			dataType: "json",
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
						
				if(!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					tecs.closeConfirmSimReplaceDialog();
					search.applyConfigurations();
					main.showSuccess(response.statusMessage);
					details.getDetailsFromEntity(details.entityIdValue);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	buildRestoreEntityComponent: function() {
		$("#confirmRestoreEntity").dialog({
			dialogClass : "dialogWarning",
			draggable : false,
			resizable : false,
			width: 505,
			minHeight : 10,
			modal : true,
			autoOpen : false,
			position : { my: "top", at: "top+5", of: details.detailsComponent },
			close : function(event, ui) {
				$("#detailsOptions").selectmenu("index", 0);
			}
		});
		
		$("#confirmRestoreEntity button").button();
		
		main.registerDetailsDialogToDestroy("confirmRestoreEntity");
		
		$("#confirmRestoreEntityCancelBtn").unbind("click").click(tecs.closeRestoreEntityDialog);
		$("#confirmRestoreEntityOkBtn").unbind("click").click(tecs.sendRestoreEntityRequest);
	},
	
	openRestoreEntityDialog: function() {
		$("#confirmRestoreEntity").dialog("open");
	},
	
	closeRestoreEntityDialog: function() {
		$("#confirmRestoreEntity").dialog("close");
	},
	
	sendRestoreEntityRequest: function() {
		$.ajax({
			type: "POST",
			cache: false,
			dataType: "json",
			url: "RestoreTec?" + details.entityIdName + "=" + details.entityIdValue,
			beforeSend: function() {
				main.showOperationsLoading();
			},
			complete: function() {
				main.hideOperationsLoading();
			},
			success: function(response) {
				if(main.isSessionExpiredOrGenericError(response, null)) {
					return;
				}
				
				tecs.closeRestoreEntityDialog();
				if (!response.ok) {
					main.showWarning(response.statusMessage);
				} else {
					details.getDetailsFromEntity(details.entityIdValue);
					main.showSuccess(response.statusMessage);
				}
			},
			error: function(jqXHR, status, error) {
				if(main.isSessionExpiredOrGenericError(null, jqXHR)) {
					return;
				}
			}
		});
	},
	
	resizeContent: function() {
		main.resizeSingleContainer();
		setTimeout(function(){
			$("#tblTimelineStats_wrapper .dataTables_scrollHeadInner .dataTable").width(570);
			$("#tblTimelineStats_wrapper").width(570);
			
//			if(details.detailsComponent.hasClass('ui-dialog-content')) {
//				details.detailsComponent.dialog({position: { my: "top", at: "top+100", of: window }});
//			}
		}, 50);
	}
};

$(window).resize(tecs.resizeContent);