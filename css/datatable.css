/**
* STYLES MAPPED WITH LISTS XML
* DO NOT REMOVE OR CHANGE THESE STYLES
**/

.genericDays {
	min-width: 160px;
	text-align: right;
}

.genericUnits {
	min-width: 150px;
	text-align: right;
}

.genericSmallUnits {
	min-width: 120px;
	text-align: right;
}

.genericPercent {
	min-width: 140px;
	text-align: right;
}

.genericMs {
	min-width: 120px;
	text-align: right;
}

.genericLongText {
	min-width: 700px;
}

.genericMediumText {
	min-width: 400px;
}

.genericSmallText {
	min-width: 250px;
}

.enabledProduct {
	min-width: 80px;
}

.genericExtraSmallText {
	min-width: 140px;
}

.genericSmallId {
	min-width: 100px;
}

.genericLong {
	min-width: 100px;
}

.serialNumber {
	min-width: 150px;
}

.imei {
	min-width: 150px;
}

.imsi {
	min-width: 150px;
}

.iccid {
	min-width: 180px;
}

.msisdn {
	min-width: 120px;
}

.tecState {
	min-width: 100px;
}

.store {
	min-width: 100px;
}

.device {
	min-width: 150px;
}

.deviceType {
	min-width: 170px;
}

.confType {
	min-width: 110px;
}

.fullDate {
	min-width: 165px;
}

.mediumDate {
	min-width: 120px;
}

.username {
	min-width: 120px;
}

.channel {
	min-width: 120px;
}

.service {
	min-width: 170px;
}

.readWrite {
	min-width: 120px;
}

.category {
	min-width: 120px;
}

.operationType {
	min-width: 210px;
}

.operation {
	min-width: 170px;
}

.result {
	min-width: 140px;
}

.seller {
	min-width: 120px;
}

.fullTime {
	min-width: 100px;
}

.numericId {
	min-width: 130px;
}

.numericLargeId {
	min-width: 170px;
}

.module {
	min-width: 150px;
}

.severity {
	min-width: 150px;
}

.alarmType {
	min-width: 150px;
}

.quantity {
	min-width: 110px;
}

.site {
	min-width: 110px;
}

.region {
	min-width: 150px;
}

.agent {
	min-width: 100px;
}

.subType {
	min-width: 100px;
}

.name {
	min-width: 150px;
}

.smallName {
	min-width: 120px;
}

.type {
	min-width: 150px;
}

.tecType {
	min-width: 120px;
}

.tecVersion {
	min-width: 100px;
	text-align: right;
}

.address {
	min-width: 150px;
}

.nif {
	min-width: 150px;
}

.sapReference {
	min-width: 150px;
}

.tecAppTypes {
	min-width: 120px;
}

.eventType {
	min-width: 100px;
}

.eventData {
	min-width: 100px;
}

.subagent {
	min-width: 120px;
}

.profileType {
	min-width: 100px;
}

.available {
	min-width: 100px;
}

.inProduction {
	min-width: 120px;
}

.geofencing {
	min-width: 130px;
}


/**----------------------------------------------------------**/

/* #mainDataTable { */
/* 	width: 100% !important; */
/* } */

.dataTable {
/* 	width: 100% !important; */
	font-family: Verdana, Helvetica, sans-serif;
}

.dataTable td {
	white-space: nowrap;
}

.dataTables_paginate {
	text-align: center;
	margin-top: 100px;
}

.dataTables_paginate a {
	padding: 5px;
	padding-left: 10px;
	padding-right: 10px;
}

.dataTables_wrapper {
	margin: 0px;
}

.dataTables_wrapper .fg-toolbar {
	border-radius: 0px !important;
}
	

.dataTables_length {
	display: none;
}

.dataTables_filter {
	display: none;
}

.DataTables_sort_wrapper span {
	display: inline-block;
	float: right;
}

.DataTables_sort_wrapper {
	margin: 7px;
	margin-left: 5px;
}

.DataTables_sort_icon.css_right {
/* 	margin-right: 7px; */
}

.dataTable .ui-state-default {
	border: 0px;
	border-left: 1px solid #D96E0D;
	cursor: pointer;
	background: #F27B0F;
	color: #FFFFFF;
	font-size: 12px;
}

.dataTable .ui-state-default:first-of-type {
	border-left: 0px;
}

.dataTable .ui-state-default:hover {
	background-color: #F7931D;
}

.dataTable .ui-state-default .ui-icon {
	background-image: url(custom-theme/images/ui-icons_efefef_256x240.png) !important;
}

.dataTable tbody tr td {
	line-height: 28px;
	border-bottom: #DCDCDC 1px solid;
}

.dataTable tbody tr td span {
	margin: 0px 5px;
}

.odd {
	background-color: #FFFFFF;
}

.even {
	background-color: #EFEFEF;
}

.odd:hover, .even:hover {
	background-color: #DCDCDC;
}

.dataTables_scrollBody .text_right {
	text-align: right;
}

.dataTables_scrollHead {
	background: #F27B0F !important;
}

.dataTables_scroll {
	background-color: #CECECE;
	border-bottom: #F27B0F solid 1px;
}

.dataTables_info {
	font-family: Verdana, Helvetica, sans-serif;
	font-size: 11px;
	font-weight: normal;
	text-align: right;
	padding: 3px;
	padding-right: 5px;
}

.selected {
	background-color: #DCDCDC;
}

.leftAlignedColumn {
	text-align: left;
}

.rightAlignedColumn {
	text-align: right;
}

.leftAlignedWithPaddingColumn {
	text-align: left;
	padding-left: 3px;
}

.rightAlignedWithPaddingColumn {
	text-align: right;
	padding-right: 3px;
}

/**
* SEVERITY STYLES
* DO NOT REMOVE OR CHANGE THESE STYLES
**/

.severity_critical {
	background-image: url("../images/icons/sem_red.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px;
}

.severity_major {
	background-image: url("../images/icons/sem_orange.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px;
}

.severity_minor {
	background-image: url("../images/icons/sem_yellow.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px;
}

.severity_warning {
	background-image: url("../images/icons/sem_blue.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px;
}

.severity_clear {
	background-image: url("../images/icons/sem_green.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px;
}

.severity_unknown {
	background-image: url("../images/icons/sem_grey.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px;
}

.provisionStatus {
	min-width: 90px;
}

table#tblTransactionsTotals.dataTable {
	width: 100% !important;
}
