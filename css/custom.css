body {
    font-family: Verdana, Helvetica, sans-serif;
    font-size: 11px !important;
    background-color: #ffffff;
    color:#6A6A6A;
    min-width: 1180px;
    min-height: 480px;
/*     width: 100%; */
    /* overflow: hidden;  */
    
    overflow: auto;
}

a {
    color:#6A6A6A;
    text-decoration:none;
}
a:hover {
    color:#F27B0F;
}

input {
	font-family: Verdana, Helvetica, sans-serif !important;
	font-size: 11px !important;
}

input[type="text"], input[type="password"] {
	border: 1px solid #B8B8B8;
	border-radius: 3px;
    height: 21px;
    padding-left: 5px;
    color: #6A6A6A;
    background-color: #FFFFFF;
}

button {
	height: 25px;
}

#footer {
	height: 30px;
}

#footer span {
	display:inline-block;
	float: right;
	line-height: 20px;
	vertical-align: middle;
	margin-top: 10px;
	margin-right: 10px;
}

#body, #header, #footer {
	width: 100%;
    overflow: visible;
}

.dialogNoTitle .ui-dialog-titlebar, .innerDialogDetails .ui-dialog-titlebar {
	display: none;
}

#loginDialog {
	font-size: 11px;
	line-height: 30px;
	vertical-align: middle;
	padding: 10px 10px;
/* 	background-color: #EFEFEF; */
	visibility: hidden;
}

#loginDialog input[type="text"], #loginDialog input[type="password"] {
	float: right;
	width: 150px;
}

#loginDialog label {
	width: 85px;
    float: left;
}

#resetPasswordDialog{
	font-size: 11px;
	line-height: 30px;
	vertical-align: middle;
	padding: 10px 10px;
/* 	background-color: #EFEFEF; */
	visibility: hidden;
}

#resetPasswordDialog input[type="text"], #loginDialog input[type="password"] {
	float: right;
	width: 150px;
}

#resetPasswordDialog label {
	width: 85px;
    float: left;
}

#loadingScreen {
	width: 100%;
	height: 100%;
	background-color: #FFFFFF;
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 2000;
}

#tableLoading {
	width: 100%;
	background-color: #FFFFFF;
	position: absolute;
	left: 0px;
	z-index: 1900;
	display:none;
}

#innerTableLoading {
	width: 100%;
	background-color: #FFFFFF;
	position: absolute;
	left: 0px;
	z-index: 1900;
	display:none;
}

#operationsLoading {
	width: 100%;
	height: 100%;
	background-color: #FFFFFF;
	opacity: 0.5;
	position: absolute;
	top: 0px;
	left: 0px;
	z-index: 1900;
	display:none;
}

#loadingScreen table, #tableLoading table, #operationsLoading table, #innerTableLoading table {
	width: 100%;
	height: 100%;
}

#loadingScreen td, #tableLoading td, #operationsLoading td, #innerTableLoading td {
	text-align: center;
	vertical-align: middle;
}

.feedbackContainer {
	position: absolute;
	top: 90px;
	left: 25%;
	width: 50%;
	display: none;
	z-index: 5000;
}

#warningFeedback {
	background-color: #FFE5E5;
	color: #FF0000;
}

#successFeedback {
	background-color: #D9EFDA;
	color: #42AE46;
}


.feedbackContainer table {
	width: 100%;
}

.feedbackContainer table td {
	text-align: left;
	vertical-align: middle;
	padding: 10px;
	font-size: 11px;
	font-weight: bold;
}

.feedbackContainer table td:first-of-type {
	width: 32px;
}

.ui-accordion-content input[type="submit"] {
	margin: 3px 5px;
}

.left {
    float: left;
}

.right {
    float: right;
}

.leftSimpleTable .fg-toolbar {
	display: none;
}

.leftSimpleTable .dataTables_scroll {
	border-bottom: 0px;
}

.leftSimpleTable.ui-accordion-content {
	background-color: #EDEDED !important;
}

.statusSuccess {
	background-image: url("../images/icons/success.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 22px; 
	color: #42AE46;
	margin-left: 5px;
	margin-right: 5px;
}

.statusError {
	background-image: url("../images/icons/warning.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 22px; 
	color: #FF0000;
	margin-left: 5px;
	margin-right: 5px;
}

.statusChanged {
	background-image: url("../images/icons/changed.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 22px; 
	color: #F7B200;
	margin-left: 5px;
	margin-right: 5px;
}

.statusWarning {
	background-image: url("../images/icons/verify.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 22px; 
	color: #F7B200;
	margin-left: 5px;
	margin-right: 5px;
}

.simpleStatusTable tr:hover {
	cursor: pointer;
}

.simpleStatusTable td {
	vertical-align: middle;
}

.dialogWarning .ui-dialog-titlebar {
	background: #F27920 !important;
/* 	padding: 10px; */
	color: #FFFFFF;
	border: 0px;
}

.dialogWarning .ui-dialog-title {
	line-height: 34px;
	vertical-align: middle;
	font-size: 14px;
}

.dialogWarning .ui-dialog-titlebar-close {
	display: none;
}

.dialogWarning .ui-button {
	width: 120px !important;
}

.dialogWarning .content {
	padding: 10px;
	font-size: 11px;
	line-height: 18px;
}

.content input[type="text"]{
	width:98%;
}

.content.smallInputs input{
	width:160px;
}

.simpleStatusTable .elementMachine {
	background-image: url("../images/icons/machine.png");
	background-repeat: no-repeat;
	background-position: 5px center;
	padding-left: 30px; 
}

.simpleStatusTable .elementProcess {
	background-image: url("../images/icons/process.png");
	background-repeat: no-repeat;
	background-position: 5px center;
	padding-left: 30px; 
}

.simpleStatusTable .elementSystem {
	background-image: url("../images/icons/system.png");
	background-repeat: no-repeat;
	background-position: 5px center;
	padding-left: 30px; 
}


.ui-progressbar.ui-widget-content {
	border: 1px solid #B8B8B8;
	background-color: #efefef;
}

.ui-progressbar-value.ui-widget-header {
	background: #EFAA6E;
}

.fileinput-button.ui-state-default {
	color: #6A6A6A !important;
}

.fileinput-button.ui-state-hover {
	background: #C7C7C7 !important;
}

#exportMailAddress {
	width: 480px;
}

#innerExportMailAddress {
	width: 480px;
}

#confirmExportToMail {
	display: none;
}

#innerConfirmExportToMail {
	display: none;
}

#addTecToBlacklist {
	display: none;
}

#removeTecFromBlacklist {
	display: none;
}

#mobileTecInstallation {
	display: none;
}

#startMobileMsellerReinstallation{
	display: none;
}

#addTecToBlacklistReason {
	width: 480px;
}

#removeTecFromBlacklistReason {
	width: 480px;
}

#resetTecAppVersion {
	display: none;
}

#removeTec {
	display: none;
}

#removeTecReason {
	width: 480px;
}

#confirmTec {
	display: none;
}

#confirmTecReason {
	width: 480px;
}

#confirmingTec {
	display: none;
}

#activateTec {
	display: none;
}

#activateTecReason {
	width: 480px;
}

#deactivateTec {
	display: none;
}

#deactivateTecReason {
	width: 480px;
}

#disassociateTec {
	display: none;
}

#disassociateTecReason {
	width: 480px;
}

#addSellerToBlacklist {
	display: none;
}

#removeSellerFromBlacklist {
	display: none;
}

#generateSellerTecPin {
	display: none;
}

#showGeneratedSellerTecPin {
	display: none;
}

#generateSellerIaPassword {
	display: none;
}

#showGeneratedSellerIaPassword {
	display: none;
}

#removeSeller {
	display: none;
}

#generateAgentTecPin {
	display: none;
}

#showGeneratedAgentTecPin {
	display: none;
}

#generateInstallerUserTecPin {
	display: none;
}

#showGeneratedInstallerUserTecPin {
	display: none;
}

#associateTec {
	display: none;
}

#associatingTec {
	display: none;
}

.transactionsTotalsStatus {
	width: 100%;
	padding: 5px;
	background-color: #E6E6E6;
}

.transactionsTotalsStatus td:last-of-type {
	text-align: right;
}

#pendingTransactionsTotalsContainer {
	background-color: #EDEDED;
	width: 100%; 
	height: 150px;
	
}

#pendingTransactionsTotalsContainer td:first-of-type{
	width: 800px;
}

.messageTextArea {
	max-width: 477px;
	max-height: 75px;
	width: 98%;
}

#sendNotificationSubject {
	width: 98%;
}

#checkboxContainerLeft {
	float: left; 
	width:50%;
}
#checkboxContainerRight {
	float: right; 
	width:50%;
}
#checkboxNote {
	 padding-left:18px;
	 font-size:90%;
}
#mManagerDialog {
	text-align: center;
	font-size: 20px;
	line-height: 30px;
	vertical-align: middle;
	padding: 10px 10px;
 	background-color: #EFEFEF;
	visibility: hidden;
}
#downloadDiv{
	visibility: hidden;
}

.three {
    display: inline-block;
    vertical-align: top;
    width: 32%;
    height: 90%;
}
.inside-three {
	width: 100%;
	font-size: 15px;
	padding: 10px;
	padding-top: 30px;
	text-align: center;
	vertical-align: middle;
}
.button-downlod {
	display: inline-block;
	outline: none;
	cursor: pointer;
	text-align: center;
	text-decoration: none;
	font: 14px/100% Arial, Helvetica, sans-serif;
	padding: .5em 2em .55em;
	text-shadow: 0 1px 1px rgba(0,0,0,.3);
	-webkit-border-radius: .5em; 
	-moz-border-radius: .5em;
	border-radius: .5em;
	-webkit-box-shadow: 0 1px 2px rgba(0,0,0,.2);
	-moz-box-shadow: 0 1px 2px rgba(0,0,0,.2);
	box-shadow: 0 1px 2px rgba(0,0,0,.2);
}
.button-downlod:hover {
	text-decoration: none;
}
.button-downlod:active {
	position: relative;
	top: 1px;
}

.numberCircle {
	display: inline-block;  
    border-radius: 50%;

    width: 36px;
    height: 36px;
    padding: 8px;
    
    background: #F17C0F;
    color: #fdfcff;
    text-align: center;
    
    font: 32px Arial, sans-serif;
}
.image-download-mManager {
	max-width:95%;
	max-height:95%;
}
/* Tablet Portrait */
@media screen and (orientation: portrait) {
	.three {
	    display: inline-block;
	    vertical-align: top;
	    width: 100%;
	    height: 33%;
	    border-bottom: 1px solid black;
	    padding-bottom: 50px;
	}
	.image-download-mManager {
		max-width:70%;
		max-height:70%;
	}
	.inside-three {
		width: 100%;
		font-size: 30px;
		padding: 10px;
		padding-top: 30px;
		text-align: center;
		vertical-align: middle;
	}
	#footer {
		visibility: hidden;
	}
	.div-no-border {
		border-bottom: 0px !important;
		margin-bottom: 50px;
	}
	.numberCircle {
		display: inline-block;  
	    border-radius: 50%;
	
	    width: 65px;
	    height: 65px;
	    padding: 8px;
	    
	    background: #F17C0F;
	    color: #fdfcff;
	    text-align: center;
	    
	    font: 55px Arial, sans-serif;
	}
	.button-downlod {
		font: 25px/100% Arial, Helvetica, sans-serif;
	}
	body{
		overflow: scroll !important;
	}

}
