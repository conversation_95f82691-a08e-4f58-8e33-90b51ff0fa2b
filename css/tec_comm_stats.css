#chartBreadCrumb {
	width: 290px;
}

#infoContainer {
	margin: 10px;
	background-color: #CECECE !important;
	font-size: 13px !important;
}

#infoContainer td {
	vertical-align: middle;
	padding-right: 10px;
}

.messagesListTitle {
	font-weight: bold;
}

#infoChartContainer div {
	background-color: #efefef;
	font-size: 11px;
}

#infoChartContainer div .ui-buttonset .ui-button {
	margin-right: -5px;
}

#infoChartContainer div .ui-button-text-only {
	color: #6A6A6A;
}

#infoChartContainer div .ui-state-hover,  #infoChartContainer div .ui-state-active {
	background: #C7C7C7
}

#infoChartContainer div td {
	vertical-align: middle;
}
