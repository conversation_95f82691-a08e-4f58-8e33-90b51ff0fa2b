.dialogDetails .ui-dialog-titlebar {
	background: #F27920 !important;
	color: #FFFFFF;
	border: 0px;
}

.dialogDetails .ui-dialog-title {
	line-height: 34px;
	vertical-align: middle;
	font-size: 14px;
}

.dialogDetails .detailsBodyContent {
	overflow: auto;
}

.innerDialogDetails .detailsBodyContent {
	overflow: auto;
}

.detailsContent .detailsContainer, .detailsContent .innerMapContainer {
	font-family: Verdana, Helvetica, sans-serif !important;
	font-size: 11px !important;
	width: 100% !important;
}

.detailsContent .detailsContainer th, .detailsContent .innerMapContainer th {
	font-size: 12px;
	font-weight: bold;
	text-align: left;
	line-height: 24px;
	vertical-align: middle;
	padding-left: 10px;
	background-color: #6A6A6A;
	width: 530px;
}

.detailsContent .detailsContainer th:last-of-type, .detailsContent .innerMapContainer th:last-of-type {
	border-left: 1px solid #6A6A6A;
	padding-left: 0px;
	width: 260px;
}

.detailsContent .detailsContainer td {
	border-top: 1px solid #B7B7B7;
	font-weight: normal;
	height: 30px;
	vertical-align: middle;
}

.detailsContent .innerMapContainer td {
	border-top: 1px solid #B7B7B7;
	font-weight: normal;
	vertical-align: middle;
}

.detailsContent .detailsContainer td div {
	margin-left: 10px;
	margin-right: 3px;
	line-height: 23px;
}

.detailsContent .detailsContainer tr td:first-of-type  {
	width: 290px;
	background-color: #F0F0F0;
	font-weight: bold;
}

.detailsContent .innerMapContainer tr td:first-of-type  {
	width: 250px;
}

.detailsContent .detailsContainer tr td:last-of-type {
	border-left: 1px solid #B7B7B7;
	width: 400px;
	background-color: #FFFFFF;
}

.detailsContent .innerMapContainer tr td:last-of-type {
/* 	border-left: 1px solid #B7B7B7; */
	width: 440px;
}

.detailsContent th .ui-selectmenu {
	height: 24px;
	border: 0px;
	background-color: #E2E2E2;
}

.detailsContent th .ui-selectmenu-status {
	line-height: 14px !important;
	font-size: 12px !important;
	background-color: #6A6A6A;
	color: #FFFFFF !important;
}

.detailsContent th .ui-selectmenu {
	border-radius: 0px !important;
}

.detailsContent th .ui-selectmenu.ui-state-active,
.detailsContent th .ui-selectmenu.ui-state-default {
	background: #6A6A6A !important;
	padding-right: 1px;
}

.detailsContent th .ui-icon {
	background-image: url(custom-theme/images/ui-icons_efefef_256x240.png) !important;
}

.detailsContent .ui-buttonset .ui-button {
	margin-right: -5px;
}

.detailsContent .ui-button-text-only {
	color: #6A6A6A;
}

.detailsContent .ui-state-hover,  .detailsContent .ui-state-active {
	background: #C7C7C7
}

.detailsContent .ui-state-disabled {
	opacity: 0.8;
	filter: Alpha(Opacity=80);
}

.reversegeocoding {
	float: left;
}

#saleStatsTableCharts .fg-toolbar {
	display: none;
}

#saleStatsTableCharts {
	width: 100%;
	font-size: 11px;
}

#saleStatsTableChartsContainer {
	padding: 10px;
}

.statsTitle {
	font-weight: bold;
	padding-bottom: 10px;
}

.smallChartContainer {
	height: 225px;
}

#tblSalesStats.dataTable tr:last-of-type td {
	border-bottom: 1px solid #F27B0F;
}

.statsTableWithBottomBorder  tr:last-of-type td {
	border-bottom: 1px solid #F27B0F;
}

.statsTableFooterWithTopBorder {
	border-top: 1px solid #F27B0F;
}

#statsTable_wrapper, #auditStatsTable_wrapper {
	margin-right: 20px;	
}

.statsTecsDialog #chartDiv {
	width:60%;
	height:500px;
	float:left;
	background: #FFFFFF;
}

.statsAuditDialog #auditPieChartDiv {
	width:60%;
	height:400px;
	float:left;
	background: #FFFFFF;
}

.statsTecsDialog #tabularDataDiv, .statsAuditDialog #auditTabularDataDiv {
	width:40%;
	height:500px;
	float:left;
	background: #FFFFFF;
	font-size: 11px;
}

.statsGroupByDiv {
	padding:20px 0;	
}


.statsAuditDialog #auditBarChartDiv {
	width:100%;
	height:400px;
	float:left;
	background: #FFFFFF;
}

.statsAuditDialog #auditTabularDataDiv {
	width:40%;
	height:400px;
	float:left;
	background: #FFFFFF;
	font-size: 11px;
}
.auditStatsGroupByDiv {
	padding:20px;	
	background-color: #FFFFFF;
	float: right;
}



/**
FIELDS LIST (TEC MESSAGES)
**/

.fieldsListContent {
	background-color: #EDEDED;
	overflow: auto;
	padding-top: 20px;
}

.fieldsListContent table {
	width: 100%;
	display: none;
}

.fieldsListContent table td {
	vertical-align: top;
	padding: 10px;
}

#referenceDataContainer .fieldsListContent table td {
	vertical-align: baseline;
	padding: 10px;
}

#referencePubContainer .fieldsListContent table td {
	vertical-align: baseline;
	padding: 10px;
}

.fieldsListContent table td.notificationMessage {
	vertical-align: middle;
	padding: 5px;
}

.fieldsListContent table td.statusIcon {
	width: 20px;
	text-align: center;	
}

.fieldsListContent table td.notificationIcon {
	width: 30px;
	text-align: center;	
	padding: 5px;
}

.fieldsListContent table td.statusIcon, .fieldsListContent table td.notificationIcon {
	width: 30px;
	text-align: center;	
}

.fieldsListContent table td.statusIcon img {
	display: none;
}

.fieldsListContent table td.valueItem {
	width: 210px;
}

.fieldsListContent table td.valueItemLong {
	width: 300px;
}

.fieldsListContent table td.valueItemLong input {
	width: 300px;
}

#referencePubContainer .fieldsListContent table td.valueItemLong input {
	width: 500px;
}

.fieldsListContent table td.valueItem div {
	text-align: right;
	float: right;
	font-size: 11px;
	font-style: italic;
}

.fieldsListContent table td:last-of-type {
	font-size: 11px;
}

.fieldsListContent textarea {
/* DO NOT CHANGE VALUES, TEXTAREA WILL NOT WORK!!!!!!*/
	font-family: Courier New;
	font-size: 16px;
	width: 210px;
	height: 40px;
	line-height: 20px;
	resize: none;
	padding: 0px !important;
}

.fieldsListBtnsContainer .fieldsListCleanBtn{
	float: left;
}

.fieldsListBtnsContainer {
	display: none;
	margin: 5px;
	margin-top: 7px;
	float: right;
}

.fieldsListContent .ui-buttonset .ui-button {
	margin-right: -5px;
}

.fieldsListContent .ui-button-text-only {
	color: #6A6A6A;
}

.fieldsListContent .ui-state-hover,  .fieldsListContent .ui-state-active {
	background: #C7C7C7
}

.fieldsListContent table.verticalMiddle td {
	vertical-align: middle;
}

.invalidValue {
	background-color: #FFE5E5!important;
	color: #FF0000!important;
}

.mosaicElement {
	display: none;
}

.mosaicContent {
	padding: 10px;
	overflow: auto;
}

.mosaicContent .mainElement {
	width: 100%;
}

.mosaicContent .subElement {
	width: 200px;
	float: left;
	margin-right: 10px;
}

.elementTitle {
	float:left;
	width: 100%;
	background-color: #F27B0F;
	color: #FFFFFF;
	font-weight: bold;
}

.elementTitle span {
	margin: 10px;
	line-height: 30px;
}

.elementContent {
	float:left;
	width: 100%;
	background-color: #FFFFFF;
	margin-bottom: 10px;
	font-size: 11px;
}

.elementContentPart {
	float: left;
	margin: 10px;
}

.elementContentPart span {
	font-weight: bold;
	line-height: 30px;
}

.elementContentPart img {
	margin: 10px 0px;
}

.elementChartPresentaion {
	height: 64px;
}

.elementChartPresentaion img {
	margin: 0px;	
}

.elementChart {
	width: 70px;
	height: 60px;
	vertical-align: middle;
}

.elementChartInfo {
	height: 32px;
	vertical-align: middle;
}

.elementStatus {
	float: left;
	height: 30px;
	margin-top: 10px;
}

.elementStatus.statusWarning, 
.elementStatus.statusError, 
.elementStatus.statusSuccess {
	background-position: 0px top;
	margin: 0px;
}

.elementUserOptions {
	line-height: 16px;
	margin-bottom: 2px;
}

.optionStart {
	background-image: url("../images/icons/start.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px; 
	color: #FF0000;
	cursor: pointer;
}

.optionStop {
	background-image: url("../images/icons/stop.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px; 
	color: #42AE46;
	cursor: pointer;
}

.optionChecking {
	background-image: url("../images/icons/verify.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px; 
	color: #F7B200;
}

.optionRestart {
	background-image: url("../images/icons/restart.png");
	background-repeat: no-repeat;
	background-position: 0px center;
	padding-left: 20px; 
	color: #F7B200;
	cursor: pointer;
}

.innerForm {
	border: 0px;
	vertical-align: middle;
}

.innerForm td {
	border: 0px !important;
	background-color: #FFFFFF !important;
}

.detailsContainer .divclearable {
	width: 385px;
	padding-left: 0px;
}

.detailsContainer tr td:last-of-type div.divclearable {
	margin-left: 5px;
}

.detailsContainer td input {
	width: 362px;
}

.detailsContainer tr td .ui-selectmenu {
	height: 28px;
	border: 1px solid #FFFFFF;
}

.detailsContainer tr td .ui-selectmenu-status {
	line-height: 20px !important;
	font-size: 12px !important;
}

.detailsContainer tr td .ui-selectmenu {
	border-radius: 0px !important;
}

.detailsContainer .generalData {
	float: left;
}





.entityContextContent .detailsContainer {
	font-family: Verdana, Helvetica, sans-serif !important;
	font-size: 11px !important;
	width: 100% !important;
}

.entityContextContent .detailsContainer th {
	font-size: 12px;
	font-weight: bold;
	text-align: left;
	line-height: 24px;
	vertical-align: middle;
	padding-left: 10px;
	background-color: #6A6A6A;
	width: 70px;
}

.entityContextContent .detailsContainer th:last-of-type {
	border-left: 1px solid #6A6A6A;
	padding-left: 0px;
	width: 330px;
}

.entityContextContent .detailsContainer td {
	border-top: 1px solid #B7B7B7;
	font-weight: normal;
	height: 30px;
	vertical-align: middle;
}

.entityContextContent .detailsContainer td div {
	margin-left: 10px;
	margin-right: 3px;
	line-height: 23px;
}


.entityContextContent .detailsContainer tr td:first-of-type {
	width: 150px;
	background-color: #F0F0F0;
	font-weight: bold;
}

.entityContextContent .detailsContainer tr td:last-of-type {
	border-left: 1px solid #B7B7B7;
	width: 250px;
	background-color: #FFFFFF;
}

.entityContextContent th .ui-selectmenu {
	height: 24px;
	border: 0px;
	background-color: #E2E2E2;
}

.entityContextContent th .ui-selectmenu-status {
	line-height: 14px !important;
	font-size: 12px !important;
	background-color: #6A6A6A;
	color: #FFFFFF !important;
}

.entityContextContent th .ui-selectmenu {
	border-radius: 0px !important;
}

.entityContextContent th .ui-selectmenu.ui-state-active,
.entityContextContent th .ui-selectmenu.ui-state-default {
	background: #6A6A6A !important;
}

.entityContextContent th .ui-icon {
	background-image: url(custom-theme/images/ui-icons_efefef_256x240.png) !important;
}


.dialogContextMenu .ui-dialog-titlebar {
	background: #F27920 !important;
	color: #FFFFFF;
	border: 0px;
}

.dialogContextMenu .ui-dialog-title {
	line-height: 24px;
	vertical-align: middle;
	font-size: 13px;
}

.dialogContextMenu .ui-dialog-titlebar-close {
	display: none;
}

#tecGenerateTokenButton {
	width: 90px;
	color: #6a6a6a;
}

#inactivePermissions, #activePermissions {
	list-style-type: none; 
	margin: 0; 
	overflow: auto;
}

#inactivePermissions li, 
#activePermissions li { 
	font-size: 11px; 
	border-top: #BABABA 1px solid;
}

#inactivePermissions li:last-of-type, 
#activePermissions li:last-of-type { 
	border-bottom: #BABABA 1px solid;
	margin-bottom: 5px;
}

#inactivePermissions li:hover, 
#activePermissions li:hover { 
	cursor: pointer;
	background-color: #EFEFEF;
	color: #6A6A6A;
}

#inactivePermissions li div, 
#activePermissions li div { 
/* 	padding: 7px 5px; */
}

#inactivePermissions li div:first-of-type, 
#activePermissions li div:first-of-type{ 
	width: 30px;
	padding: 7px 5px;
	float: left;
}

#inactivePermissions li div:last-of-type, 
#activePermissions li div:last-of-type { 
	width: 240px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding: 7px 5px;
}

#inactiveProductChannels, #activeProductChannels {
	list-style-type: none; 
	margin: 0; 
	overflow: auto;
}

#inactiveProductChannels li, 
#activeProductChannels li { 
	font-size: 11px; 
	border-top: #BABABA 1px solid;
}

#inactiveProductChannels li:last-of-type, 
#activeProductChannels li:last-of-type { 
	border-bottom: #BABABA 1px solid;
	margin-bottom: 5px;
}

#inactiveProductChannels li:hover, 
#activeProductChannels li:hover { 
	cursor: pointer;
	background-color: #EFEFEF;
	color: #6A6A6A;
}

#inactiveProductChannels li div, 
#activeProductChannels li div { 
/* 	padding: 7px 5px; */
}

#inactiveProductChannels li div:first-of-type, 
#activeProductChannels li div:first-of-type{ 
	width: 30px;
	padding: 7px 5px;
	float: left;
}

#inactiveProductChannels li div:last-of-type, 
#activeProductChannels li div:last-of-type { 
	width: 240px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding: 7px 5px;
}

#navButtons {
	width: 45px;
	text-align: center;
}

.productChannelsContainer {
	width: 690px;
	padding: 5px;
}

.productChannelsListContainer {
	width: 320px;
	border: 1px solid #BABABA;
}

.productChannelsListTitle {
	padding: 5px;
	background-color: #6A6A6A;
	color: #FFFFFF;
	vertical-align: middle;
	height: 20px;
}

.productChannelsDescSearch {
	line-height: 46px;
	vertical-align: middle;
}

#productChannelsDescription {
	width: 365px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.productChannelsDescSearch td:last-of-type {
	text-align: right;
}

.productChannelsDescSearch input {
	width: 250px;
	padding-top: 0px !important;
	padding-bottom: 2px !important;
	margin: 0 !important;
	font-size: 12px !important;
}

.productChannelsFullDesc td {
	height: 70px;
	vertical-align: middle;
}

.productChannelsFullDesc textarea {
	width: 674px;
	background-color: #FFFFFF !important;
	resize: none;
}

.permissionsContainer {
	width: 690px;
	padding: 5px;
}

.permissionsListContainer {
	width: 320px;
	border: 1px solid #BABABA;
}

.permissionsListTitle {
	padding: 5px;
	background-color: #6A6A6A;
	color: #FFFFFF;
	vertical-align: middle;
	height: 20px;
}

.profileDescSearch {
	line-height: 46px;
	vertical-align: middle;
}

#profileDescription {
	width: 365px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.profileDescSearch td:last-of-type {
	text-align: right;
}

.profileDescSearch input {
	width: 250px;
	padding-top: 0px !important;
	padding-bottom: 2px !important;
	margin: 0 !important;
	font-size: 12px !important;
}

.permissionFullDesc td {
	height: 70px;
	vertical-align: middle;
}

.permissionFullDesc textarea {
	width: 674px;
	background-color: #FFFFFF !important;
	resize: none;
}

.searchHighlight {
	background-color: #F27B0F;
	color: #FFFFFF;
}

#removeProfile {
	display: none;
}

#generateAgentIaPassword {
	display: none;
}

#showGeneratedAgentIaPassword {
	display: none;
}

#agentTransferBalance, #editAgentBalance {
	display: none;
}

#createTec, #salesTecStats, #auditStats {
	display: none;
}


#removeSubAgent,
#generateSubAgentTecPin,
#showGeneratedSubAgentTecPin,
#generateSubAgentIaPassword,
#showGeneratedSubAgentIaPassword,
#subAgentTransferBalance,
#addSubAgentToBlacklist,
#removeSubAgentFromBlacklist {
	display: none;
}

#removeStore,
#addStoreToBlacklist,
#removeStoreFromBlacklist,
#associateTec,
#associatingTec,
#generateInstallPin,
#showCreatedSellerId,
#confirmGetVoucherActivationCode,
#confirmResendProofOfPayment,
#showVoucherActivationCode 
#createPartnerTECDialog{
	display: none;
}

#sellerTransfer {
	display: none;
}

.deletedWarning {
	padding-left: 10px;
	background-color: #FFE5E5 !important;
	color: #FF0000;
}

.blackListedWarning, .inactivedWarning {
	padding-left: 10px;
	background-color: #C9E6EF !important;
	color: #10A1D7;
}

.hasPendingUpdatesWarning {
	padding-left: 10px;
	background-color: #f4dcca !important;
	color: #F27920;
}

.noGeofencingWarning {
	padding-left: 10px;
	background-color: #f4dcca !important;
	color: #F27920;
	height: 30px;
	font-weight: bold !important;
}

.salesLimitSeparator {
	background-color: #D0CFCF !important;
	text-align: left;
	line-height: 30px;
	padding-left: 10px;
}


#geofencingTreeButtonsContainer {
	height: 35px;
	vertical-align: middle;
	text-align: right;
	padding-right: 5px;
}

#geofencingRegionsListContainer {
	height: 305px;
	overflow: auto;
	border: 0px !important;
}

#geofencingRegionsTree td {
	border: 0px !important;
}

.regionsTreeElementName {
	height: 30px; 
	display: inline-block; 
	position: relative; 
	cursor: pointer;
	line-height: 30px;
	vertical-align: middle;
	float: right;
}

#confirmGeofencingRemove, #confirmGeofencingEdit {
	display: none;
}

#retryEmisTransaction, #resolveEmisTransaction {
	display: none;
}


.ghostDiv {
	display: none;
}