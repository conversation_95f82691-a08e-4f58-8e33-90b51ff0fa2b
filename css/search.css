.datePickerImg {
	cursor: pointer;
	vertical-align: bottom;
	margin-left: -4px;
	margin-right: 5px;
}

input.datePicker {
	width: 115px;
}

div.ui-datepicker-header.ui-corner-all {
	border-radius: 0px !important;
}

div.ui-datepicker.ui-corner-all {
	border-radius: 0px;
	border: 1px solid #BABABA;
	margin-top: 3px;
}

/* div.ui-datepicker.ui-widget-content { */
/* 	background-color: #efefef; */
/* } */

div.ui-datepicker a.ui-state-default {
	color: #6A6A6A;
	background-image: none;
}

div.ui-datepicker a.ui-state-highlight {
	color: #FFFFFF;
}

div.ui-datepicker a.ui-state-hover {
	color: #FFFFFF;
/* 	background: #C6C6C6 !important; */
}

div.ui-datepicker td.ui-datepicker-other-month {
	opacity: 1 ;
	filter: Alpha(Opacity=100);
}

div.ui-datepicker td.ui-datepicker-other-month span {
	background: #F1F1F0;
	background-image: none;
	color: #C6C6C6;
	font-style: italic;
}

.searchLabel, .fieldName {
	font-family: Verdana, Helvetica, sans-serif !important;
	font-size: 11px !important;
}

.advancedSearchMenu {
	display: none;
}

.advancedSearchMenuBtn {
	color: #6A6A6A !important;
}

.advancedSearchMenuBtn:hover {
	background: #C7C7C7 !important;
}

.dialogAdvancedSearch .ui-dialog-titlebar {
	background: #F27920 !important;
/* 	padding: 10px; */
	color: #FFFFFF;
	border: 0px;
}

.dialogAdvancedSearch .ui-dialog-title {
	line-height: 34px;
	vertical-align: middle;
	font-size: 14px;
}

.advancedSearchMenu table {
	font-family: Verdana, Helvetica, sans-serif !important;
	font-size: 11px !important;
	width: 100% !important;
}

#advancedSearchMenuContent {
	overflow: auto;	
}


.advancedSearchMenu th {
	border-bottom: 1px solid #B7B7B7;
	border-top: 1px solid #B7B7B7;
	font-size: 12px;
	font-weight: bold;
	text-align: left;
	line-height: 44px;
	vertical-align: middle;
	padding-left: 10px;
}

.advancedSearchMenu th:first-of-type {
	background-color: #D0CFCF;
/* 	width: 225px; */
}

.advancedSearchMenu th:last-of-type {
	background-color: #E2E2E2;
	border-left: 1px solid #B7B7B7;
}

.advancedSearchMenu td {
	border-top: 1px solid #B7B7B7;
	font-weight: normal;
	height: 30px;
	vertical-align: middle;
}

.advancedSearchMenu td input {
	width: 245px;
}

.advancedSearchMenu tr td:first-of-type {
	width: 225px;
	background-color: #F0F0F0 !important;
	cursor: pointer;
}

.advancedSearchMenu tr td:last-of-type {
	border-left: 1px solid #B7B7B7;
	width: 280px;
	background-color: #FFFFFF !important;
	margin: 0px;
	padding: 0px;
}

.advancedSearchMenu tr.rowHover td:first-of-type {
	background-color: #F27920 !important;
	cursor: pointer;
	color: #FFFFFF;
}

.advancedSearchMenu .notselectable {
	padding-left: 10px !important;
}

.advancedSearchMenu input[type="checkbox"] {
    display:none !important; 
}

.advancedSearchMenu input[type="checkbox"] + label {
    margin-left:10px !important;
    padding-left: 25px;
    vertical-align:middle !important;
    background:url(../images/icons/checkboxes.png) left -14px no-repeat !important;
    cursor:pointer !important;
	font-family: Verdana, sans-serif !important;
	font-size: 11px !important;
	height:14px!important; 
	display:inline-block!important;
	line-height:14px!important;
}

.advancedSearchMenu tr.checked .fieldName input[type="checkbox"] + label {
    background: url(../images/icons/checkboxes.png) left 0 no-repeat !important;
}

.advancedSearchMenu .ui-selectmenu {
/* 	width: 278px; */
	height: 28px;
	border: 1px solid #FFFFFF;
}

.advancedSearchMenu .rowHover .ui-selectmenu:not(.ui-selectmenu-disabled) {
	border: 1px solid #F27920!important;
}

.advancedSearchMenu .ui-selectmenu-status {
	line-height: 20px !important;
	font-size: 12px !important;
}

.advancedSearchMenu .ui-selectmenu {
	border-radius: 0px !important;
}

.extraOptionsContainer {
	background-color: #efefef;
	font-size: 11px;
}

.extraOptionsContainer .ui-buttonset .ui-button {
	margin-right: -5px;
}

.extraOptionsContainer .ui-button-text-only {
	color: #6A6A6A;
}

.extraOptionsContainer .ui-state-hover,  .extraOptionsContainer .ui-state-active {
	background: #C7C7C7
}

.extraOptionsContainer td {
	vertical-align: middle;
}

.invalidFilter {
	color: red !important;
}

.notsearchable {
	color: #B8B8B8;
	font-size: 10px;
	margin-left: 12px;
	font-style: italic;
}

.listWithPersonalSearchHeader {
	width: 100%;	
}

.personalSearchesContent {
	width: 300px;
}

#confirmPersonalSearchName, #confirmRemovePersonalSearch {
	display: none;
}

#personalSearchName {
	width: 300px;
}
