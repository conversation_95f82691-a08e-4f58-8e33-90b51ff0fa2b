#header {
	background-color: #efefef;
}

.userMenu.ui-dialog-content {
	height: auto !important;
	min-height:none !important;
	font-size: 11px;
}

.userMenu {
	display: none;
}

.userMenu li {
	background-color: #DDDCDC !important;
	color: #6A6A6A !important;
	padding: 10px;
	cursor: pointer;
}

.userMenu li:hover {
	background-color: #C6C6C6 !important;
}

.userMenuContent {
    width:100%;
    height:22px;
    background-color: #F17C0F;
}

.userMenuBtn {
/*     position:absolute; */
/*     right:0px; */
    cursor: pointer;
}

.userMenuBtn td:first-of-type {
    width: 150px;
    line-height: 22px;
    vertical-align: middle;
    color: #FFFFFF;
    font-weight: bold;
    text-align: right;
}

.userMenuBtn td:last-of-type {
    padding-right: 10px;
    padding-left: 10px;
}


.globalNavMenu {
	display: none;
}

.globalNavDialogNoTitle .ui-dialog-titlebar {
	display: none;
}

.globalNavDialogNoTitle.ui-dialog {
	border: 0px !important;
}

.globalNavMenu.ui-dialog-content {
	height: auto !important;
	min-height:none !important;
	font-size: 12px;
}

.globalNavMenu li a {
	color: #FFFFFF !important;
}

.globalNavMenu li {
	cursor: pointer;
	background-color: #6D6E70;
	padding: 10px;
}

.withMenuSeparator {
	border-bottom: 1px solid #CECECE;
}

.globalNavMenu li:hover {
	background-color: #9B9B9C !important;
}

.globalNavMenu li.selectedMenuItem {
	background-color: #FF9200;
}

.globalNavButton.globalNavActive {
	width: 149px;
    line-height: 60px;
    background: url(../images/buttons/menu_bg_hover.png) no-repeat top left !important;
    color: #FFFFFF !important;
	cursor: pointer;
}

.globalNavButton.globalNavActive a {
	color: #FFFFFF;
} 

.globalNavButton {
    font-weight:bold;
    text-align: center;
    width: 149px;
    height: 60px;
    background: url(../images/buttons/menu_bg.png) no-repeat top left;
    margin: 0px;
    padding: 0px;
    vertical-align: middle;
    color:#6E6F71;
}

.globalNavButton:hover {
    width: 149px;
    line-height: 60px;
    background: url(../images/buttons/menu_bg_hover.png) no-repeat top left;
    color: #FFFFFF;
    cursor: pointer;
}

.globalNavButton:hover a {
	color: #FFFFFF;
}

.globalNav {
    height: 60px;
    background:#EFEFEF;
    font-size: 14px;
    width: auto;
}

#unitelLogo {
	height: 60px;
	width: 216px;
	background: url(../images/logos/unitel_logo.png) no-repeat top left;
}

#changePasswordConfirm table tr td {
	line-height: 35px;
}
